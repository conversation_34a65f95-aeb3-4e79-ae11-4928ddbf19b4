<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院组织收看“贵州教育大讲堂”第17期《开学第一课》</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002317.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院组织收看“贵州教育大讲堂”第17期《开学第一课》
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-08-31</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1928</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px">2022年8月30日，</span><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">体育与艺术学院组织全体师生通过自行学习和集体观看的方式收看了</span><span style="font-family:仿宋_GB2312">“贵州教育大讲堂”第17期节目《开学第一课》。</span></span><br/><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 第</span><span style="font-family:仿宋_GB2312">17期《开学第一课》节目是为在</span></span><span style="font-family: 仿宋_GB2312;font-size: 21px">开学之际、喜迎二十大召开之际更好地对全省师生进行党史、国情、省情</span><span style="font-family: 仿宋_GB2312;font-size: 21px">进行</span><span style="font-family: 仿宋_GB2312;font-size: 21px">教育</span><span style="font-family: 仿宋_GB2312;font-size: 21px">而开展的</span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">展示贵州深厚的红色底蕴和贵州</span><span style="font-family:仿宋_GB2312">“黄金十年”的发展成果</span></span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px">引领全省师生植根红色沃土，秉承红色基因，激发全省教育系统感党恩、听党话、跟党走的热情</span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px">用实干开创美好未来，接续谱写民族复兴之美。</span><br/><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 在第</span><span style="font-family:仿宋_GB2312">17期《开学第一课》的节目中，从</span></span><span style="font-family: 仿宋_GB2312;font-size: 21px">奋进百年路、建功新时代、起航新征程三个篇章来展现了具有红色基因的贵州大地在中国近代历史进程中色彩浓厚的一笔，有中共一大代表邓恩铭烈士的革命事迹，有挽救了党、挽救了红军、挽救了中国革命的黎平、猴场、遵义会议</span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px">也</span><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">有贵州人民不畏艰难，勇闯高质量发展新路的</span><span style="font-family:仿宋_GB2312">“三线精神”</span></span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">还有贵州为发展教育点燃贵州教育</span><span style="font-family:仿宋_GB2312">“起航新征程”的 “音乐课”“美术课”及“语文课”。</span></span><span style="font-family: Calibri;font-size: 21px">&nbsp;</span><br/><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 体育与艺术学院全体师生在观看《开学第一课》的过程中，同学们兴致昂扬，均为贵州</span><span style="font-family:仿宋_GB2312">“黄金十年”的发展而感叹</span></span><span style="font-family: 仿宋_GB2312;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;font-size: 21px">深知今天的幸福生活是老一代革命家抛头颅、洒热血和在党</span><span style="font-family: 仿宋_GB2312;font-size: 21px">的领导下换来，其中党员同志更加坚定了自己的党员立场，牢记自己的初心、使命，同时广大学生也纷纷反映，在以后的学习生活中也将更</span><span style="font-family: 仿宋_GB2312;font-size: 21px">加刻苦，砥砺前行，将贵州的红色基因</span><span style="font-family: 仿宋_GB2312;font-size: 21px">和</span><span style="font-family: 仿宋_GB2312;font-size: 21px">红色文化发扬光大，并牢记革命嘱托，继承革命事业，为新时代贵州的发展添砖加瓦。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;font-size: 21px"><img src="/uploads/20231021/d85e9c6755899196b177ffcefece57ae.jpg" width="600" height="600" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);line-height: 37px"><span style="font-family: 宋体;font-size: 21px">&nbsp;</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
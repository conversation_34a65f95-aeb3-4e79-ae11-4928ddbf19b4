<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_喜报|研路漫漫，终得硕果</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000004879.shtml" class="text-decoration-none text-dark fw-bold">
                                                喜报|研路漫漫，终得硕果
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-23</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1448</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">这是一场与自己的较量</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">也是一场智慧的较量</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">从知识的海洋中寻觅答案</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">从题目的迷宫里寻找出路</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">每一个夜晚的挑灯夜读</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">每一次的挫败与坚持</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">铸就了今天的辉煌</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">在2024年全国硕士研究生招生考试中</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">我院10名考研学子</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">不负众望</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">创下佳绩</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(51, 51, 51); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; letter-spacing: 1px; white-space: normal; background-color: rgb(255, 255, 255); text-align: center; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">特此向考研成功上岸的学子表示祝贺</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">录取名单</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="color: #888888; font-family: 宋体;">陈定登</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">赵思念</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州师范大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">韩明霞</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">张鹏妃</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">周蝶</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管2班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州财经大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">陈佳俊</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管2班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">湖北民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">张天文</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管2班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">西北民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">王莎莎</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级旅管3班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">胡品军</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级文产1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州民族大学</span></p><p style="text-align: center;"><br/></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">王柯茜</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">20级文产1班</span></p><p style="text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; font-size: 16px; font-family: 宋体; line-height: 2em; color: #888888;">贵州大学</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
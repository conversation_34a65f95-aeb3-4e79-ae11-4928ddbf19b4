<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术让校园充满阳光和活力——我校第八届体育艺术节开幕了！</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002370.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术让校园充满阳光和活力——我校第八届体育艺术节开幕了！
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-05-20</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1738</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">5月20日</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">-</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">22日，</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 21px">由贵阳人文科技学院主办，贵阳人文科技学院体育与艺术学院、校工会、校团委承办的贵阳人文科技学院第八届校园体育艺术节</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">在大学城校区足球场举办</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">，</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">本次体育艺术节的母的在于</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><span style="font-family:仿宋_GB2312">弘扬体育精神，倡导同学们吃苦耐劳和顽强拼搏的精神。同时进一步丰富校园文化，展示大学生风采，促进学校体育文化更好地发展，让学校全体师生更进一步地热爱体育，参加体育，融入体育运动，提高全体学生的综合素质，加强我校大学生的文明修养增强我校师生身体素质健康发展，以良好的精神面貌积极面对生活，争取做到</span><span style="font-family:仿宋_GB2312">“德、智、体、美、劳”的全面发展</span></span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><img src="/uploads/20231021/6f0768d9337c1ab2ab3345fc717ecbd6.png" alt="image.png"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">同学们把热血挥洒在大地上，载鹏程之志，意气少年风姿昂扬，让朝气满溢于苍穹下，迎着夕阳，不负热爱，不负青春。体育与艺术学院的学子</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">青春灼灼，其焰烈烈；</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">飒爽英姿，铿锵有力举起鲜艳的红旗挺着胸膛昂扬迈进整齐的脚步与高呼的号令都在书写着少年与时代的共振同频。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><img src="/uploads/20231021/99258e35df15ca7cc895040218d23d49.png" alt="image.png"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">此次活动通过多种多样的形式吸引了更多同学的参加，经过同学们和裁判员们的努力，本次活动取得了圆满的成功。</span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><span style="font-family:仿宋_GB2312">各位同学们都以此次运动会为契机，不断加强锻炼，增强体魄。同时参加运动会的运动员们在困难面前，不畏惧，勇往直前；在胜利面前胜不骄，再接再砺；在失败面前败不馁，振作精神。发扬</span><span style="font-family:仿宋_GB2312">“友谊第一，比赛第二”，团结、拼搏精神，服从裁判，尊重对手，赛出风格、赛出水平、赛出成绩。本次艺术节充分</span></span><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px">展现了我校师生勇于拼搏的精神别样的风采。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><img src="/uploads/20231021/d9e4835be63ce276fc11ca10e66f7dba.png" alt="image.png"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><span style="font-family:仿宋_GB2312">我们要向他们学习，效仿他们，努力锻炼身体努力学习</span><span style="font-family:仿宋_GB2312">争取在大学里时时保持活力，做一个有激情的大学生。也相信我校的体育艺术节将会更加丰富多彩。<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;color: #222222;font-size: 21px"><img src="/uploads/20231021/87f1fca9707055135692735e6855c96e.png" alt="image.png"/></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
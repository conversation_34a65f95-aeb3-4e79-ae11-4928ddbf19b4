<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_著名字体设计师刘兵克到我院进行“我与我的字体”字体设计经验分享</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000003494.shtml" class="text-decoration-none text-dark fw-bold">
                                                著名字体设计师刘兵克到我院进行“我与我的字体”字体设计经验分享
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-11-21</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2901</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; visibility: visible;">&nbsp; 11月21日，著名字体设计师刘兵克于我校大学城校区图书馆报告厅进行主题为“我与我的字体”字体设计经验分享会。</span></p></section><section powered-by="xiumi.us" style="margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); text-align: center;"><strong style="text-indent: 0em; color: rgb(160, 160, 160); font-size: 12px; letter-spacing: 0.544px; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; visibility: visible; overflow-wrap: break-word !important;"><img src="/uploads/20231123/a9a7aa2d8da492002c27ebdf7d693e8c.jpg" alt="“我与我的字体”字体设计经验分享会现场.jpg"/></strong><br/></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; visibility: visible; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">“我与我的字体”字体设计经验分享会现场</span></strong></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; visibility: visible;">会上，我校体育与艺术学院院长雷帮齐为刘兵克颁发客座教授聘书。雷帮齐院长表示，刘兵克教授的加盟，为我院师生提供更优质的教学和专业的支持。</span></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><img src="/uploads/20231123/ff2e8d37d9519a24ce9d09ca0bce3837.jpg" alt="颁发聘书.jpg"/></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">颁发聘书</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><img src="/uploads/20231123/d4e37b92e487b0167409cfd01062d699.png" alt="3.png"/></strong></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); text-align: center;"><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0;"></section></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">刘兵克个人简介</span></strong></p></section><section powered-by="xiumi.us" style="margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); text-align: center;"><img src="/uploads/20231123/fd4c512f3a1f59cf99cc3ef52dc9a870.jpg" alt="刘兵克分享设计经验.jpg"/></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">刘兵克分享设计经验</span></strong></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">&nbsp;</span><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; text-indent: 2.25em;">分享会上，刘兵克以自己丰富的设计经验为基础，从字体设计的基本概念、设计原则、设计方法、设计软件等方面进行了深入浅出的讲解。他还通过展示自己近年来创作的多个优秀作品，向在场的师生展示了字体设计的魅力。</span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; text-indent: 2.25em;"><br/></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><span style="font-size: 18px;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: #000000; overflow-wrap: break-word !important;">见证时间的厚度与力量</span></strong></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">“做字挺有意思”在分享字体设计的基本概念时，刘兵克指出，非商业性的坚持创作和分享是非常有趣的。以他设计的字体“励志”为例，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">他将“励志”和“坚持”进行组合设计，通过字体运动看到不同的单词，因此组合成了“所谓励志，不过坚持”的精神。</strong>设计动机和作品的核心是赋予设计有“意思”，分享时会自然而然地吸引流量和获得行业认可。刘兵克还分享学习、从事自由职业、创业和爱情等主题如何激发他的创作灵感。他将自己的生活体验融入字体设计，如儿时回忆、情感使用图形、繁体字、英文字的结合设计方式，使得字体具备了丰富的内涵、动感和表情，同时也传达了情绪和情感。刘兵克的设计展示了字体设计并非仅对文字造型的设计，而是包含文化、历史、审美以及情感和意义表达的一种艺术形式。他的设计鼓励在场师生思考字体设计的多样性和创新性，引导他们深入理解字体设计的内涵和魅力。&nbsp;&nbsp;</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><span style="font-size: 18px;"><strong style="text-indent: 0em; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><br/></span></strong></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><span style="font-size: 18px;"><strong style="text-indent: 0em; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">“<span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%;">字作多情”的创业轨迹：能量－坚持－人气</span></span></strong></span><br/></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); text-align: center;"><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0;"><img src="/uploads/20231123/09b884e13da7bd452d42b8a7080b6076.png" alt="6.png" style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "/></section></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">刘兵克坚持“每日一字”已经八年了，他分享到通过自己的努力和实践，逐渐找到了自己的热情和方向。在大学时代，刘兵克自学设计软件，并将所学应用于与本专业相结合的创作中。通过向网站投稿，他找到了自己热爱的“点”，并始终坚持下来。<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">这种刻意练习，让他在学校的各项广告学比赛中积累经验，</strong>取得了20多项证书。同时，也会认真对待做印宣传单这样的小事积累市场的检验。</span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;"><br/></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-size: 18px; overflow-wrap: break-word !important;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">北京－广州－青岛－上海－回家</strong></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><img src="/uploads/20231123/55581708ccce448c5759a60c4bebf69b.png" alt="7.png"/></strong></span></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">毕业后，刘兵克怀揣着梦想，投简历想要去做大的创意。在自由职业的过程中，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">他对创业的感受进行了记录，形成了2010－2012年的字体日记。</strong>成功并不是一蹴而就的，而是需要付出时间和努力，不断地尝试和积累，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">坚持自己的能力之内就尽可能的做，总会找到属于自己的道路。</strong>“北京那个漂”图形与字体的融合是一种将生活情景融入字体的设计方法，使其更具个性化和生动感。在经历了与商业结合的创作稚拙期后，刘兵克尝试各种创新的设计方法，如2012元旦快乐中数字与中文的结合设计、英文与字体的结合设计、涂鸦字体与符号的结合设计等。</span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;"><br/></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><span style="font-size: 18px;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">分享，快乐加倍，能量也加倍</span></strong></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">刘兵克还分享了带领团队开展每日一字活动的经历。从2015年11月24日开始，他们共创作了8万多份优秀的上榜字体，每天进行点评，并设立了至尊奖等奖项，以鼓励大家通过自己的作品获得认可和自信。他强调，设计需要长期的练习，他还分享了如何寻找“问题字体”以及为“大牌”进行修改的方法，以便形成话题，为字体设计师和爱好者提供交流和展示平台。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">网络时代，刘兵克建议同学们要有针对性地选择投稿平台和主题，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">要形成量级的持续投稿</strong>。同时，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">也要接受对作品的评论时要有正能量的应对能力</strong>，去找到<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">给予鼓励的分享，快乐加倍，能量也加倍。</strong>刘兵克在成为父亲后，也将生活的幸福时刻融入到了创作中，创作了“奶爸成长记”、“尿布诗”和“向老婆致敬”等作品，将情感融入到字体设计中。刘兵克也制作了大量简单易学的教程，并出版了《字作多情》《自由“字”在-字体设计与创意》等作品集和著作，将自己的经验方法分享给更多的设计师。</span></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><br/></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><img src="/uploads/20231123/434796fd094ad0e9ec8567a1bc5cc81c.png" alt="9.png"/></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">网络词汇与老字体</span></strong></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); text-align: center;"><img src="/uploads/20231123/27e5e17550b284dd754a9623eef9ae5e.png" alt="10.png"/></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-indent: 2.25em; font-size: 12px; color: #A0A0A0;">“奶爸成长记”、“尿布诗”和“向老婆致敬”</span></strong></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; text-indent: 0em; overflow-wrap: break-word !important;"><span style="font-size: 18px;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><br/></span></strong></span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 0em; overflow-wrap: break-word !important; text-align: center;"><span style="font-size: 18px;"><strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">无细节，不字体：见证一群人的坚持</span></strong></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">刘兵克在讲解设计原则时，通过自己创作的“认养一头牛”和“刘兵克字体”为例，展示了如何运用对比、平衡、重复等设计原则来创造独特的字体风格。他还<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">分享了为“花西子品牌定制字库提案”的设计过程，以及如何优化字体布局和细节，使其具有高辨识度和方便应用。</strong>刘兵克的设计案例细节展示了字体设计从对笔画的分析、解构到组合，经过了两次提案被否，到最终确定以及团队合作耗时2年的设计过程，通过“穿插感”和“矩阵体”的提炼，成功地设计出有风情和韵味的“花西子体”的千字字库。这一设计成果证明了团队的实力，并得到了客户的认可和合作。刘兵克强调，<strong style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">与客户深入交流对设计工作的推动作用不容忽视。这有助于寻找设计方向，并在整个设计过程中提供有益的指导。具备了成功的作品基础，会迎来更多的合作机会，并作为在行业中背书的资本。</strong></span></p></section><section powered-by="xiumi.us" style="margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; box-sizing: border-box !important; overflow-wrap: break-word !important; text-align: center;"><img src="/uploads/20231123/817bc7de7459f064bb8122f6848e0b1a.png" alt="11.png"/></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">在问答环节，刘兵克认真回答了现场师生的提问，并就字体设计的发展趋势、设计与商业的结合等问题分享了个人见解。他表示，字体设计师应关注社会、关注生活，从各个方面汲取灵感，以创造出更具价值的作品。此次分享会不仅丰富了师生对字体设计的认识，还为体育与艺术学院视觉传达设计专业的发展提供了有益的启示。</span></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;"></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; box-sizing: border-box !important; overflow-wrap: break-word !important;"><img src="/uploads/20231123/17a647ebf25802273d770daff704a3f7.jpg" alt="展览现场.jpg"/></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; box-sizing: border-box !important; overflow-wrap: break-word !important;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">展览现场</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px;">刘兵克《字有乾坤》主题展览于11月21日至11月23日在师德楼举行。此次展览是一次字体设计行业的互动与交流，学生可通过展览获取更多设计思路和方式。不仅让师生们深入了解汉字文化的博大精深和独特魅力，还激发了他们对中华传统文化的热爱和自豪感。同时，此次活动也为字体设计行业提供了一个展示和交流的平台，促进了行业内的互动与合作。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; color: rgba(0, 0, 0, 0.9); text-align: center;"><img src="/uploads/20231123/dc35ce1d9281dfbe422f09273a596d93.jpg" alt="师生观展.jpg" style="font-size: 15px; text-align: center; text-indent: 2.25em;"/></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; box-sizing: border-box !important; overflow-wrap: break-word !important;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">师生观展</span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; box-sizing: border-box !important; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;"><img src="/uploads/20231123/247daf5e2cac0ec661bbe04e583b3d21.jpg" alt="合影.jpg"/></span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; box-sizing: border-box !important; overflow-wrap: break-word !important; text-align: center;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;"><strong style="color: rgba(0, 0, 0, 0.9); font-family: system-ui, -apple-system, BlinkMacSystemFont, "><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; text-indent: 2.25em; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">合影留念</span></strong></span></strong></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><br/></p><p style="margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; overflow-wrap: break-word !important;"><br/></p></section><p><br/></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_2020级行政管理等专业部分同学到花溪区燕楼镇、黔陶乡人民政府开展实习工作</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000003816.shtml" class="text-decoration-none text-dark fw-bold">
                                                2020级行政管理等专业部分同学到花溪区燕楼镇、黔陶乡人民政府开展实习工作
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-01-09</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2521</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent:37px;text-autospace:ideograph-numeric;text-align:justify;text-justify:inter-ideograph;line-height:33px"><span style=";font-family:仿宋;font-size:19px">2024年1月4日上午和1月5日下午，在我院行政管理教研室主任邹晓抒的带领下，2020级行政管理专业和劳动与社会保障专业的部分学生分别来到花溪区燕楼镇人民政府、花溪区黔陶乡人民政府进行实习。燕楼镇人民政府、黔陶乡人民政府相关工作人员分别与我院师生一行举行了实习生见面会。参加会议的分别有燕楼镇人民政府党政办负责人刘晋宇及相关工作人员、黔陶乡副乡长钱云及王亮萍主任，我院行政管理教研室主任邹晓抒、副主任杨胜美，教研室教师刘凌之、陈敏及14名实习生。</span></p><p style="text-indent:37px;text-autospace:ideograph-numeric;text-align:justify;text-justify:inter-ideograph;line-height:33px"><span style=";font-family:仿宋;font-size:19px">首先，政府相关工作人员对实习生们的到来表示热烈欢迎，并介绍政府的基本情况以及本次实习工作的相关安排，助力实习生们更好地体验实习生活。同时，也对实习生提出了实习工作要求：希望实习生能够珍惜此次实习机会，主动学习，积极参与实习工作，在实习中有所收获和成长。</span></p><p style="text-autospace:ideograph-numeric;text-align:center;line-height:33px"><img src="/uploads/20240109/35cf0df94de1282e32446443707b422a.png" alt="图片1.png"/></p><p style="text-indent:37px;text-autospace:ideograph-numeric;text-align:justify;text-justify:inter-ideograph;line-height:33px"><span style=";font-family:仿宋;font-size:19px">邹晓抒老师代表学院对燕楼镇人民政府及黔陶乡人民政府此次实习工作的安排表示感谢，并对全体实习生提出要求：希望实习生能尽快适应实习生这一全新身份，严格遵守政府的各项实习工作规章制度，严于律己，把握宝贵的学习机会，向工作人员虚心请教，积极参与政府组织的各项活动，在实践中不断锻炼和提高自己的综合能力。</span></p><p style="line-height: 150%; text-align: center;"><span style=";font-family:仿宋;line-height:150%;font-size:19px">&nbsp;<img src="/uploads/20240109/aea5e86dfc69e01debe18ae2b0111252.jpg" alt="图片2.jpg"/></span></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:37px;padding:0 0 0 0 ;text-autospace:ideograph-numeric;line-height:33px"><span style=";font-family:仿宋;font-size:19px">通过此次会议，同学们明白了此次实习的重要性，对实习生活充满了期待。</span><span style=";font-family:仿宋;font-size:19px"><span style="font-family:仿宋">双方表示，希望将实习活动变成常态化持续下去，此次实习活动的开展将为我院行政管理专业与政府部门的</span><span style="font-family:仿宋">“政教合作”推上一个新台阶。</span></span><span style=";font-family:仿宋;font-size:19px">相信在接下来的实习中，实习生们一定会在学习、实践、反思中提升自我，磨砺能力，不断探索、不断提高自己的综合素质。最后，大家进行了合影留念。</span><img src="/uploads/20240109/e08109e4914e9e0436173458b90252b2.png" alt="图片3.png" style="font-family: 仿宋; font-size: 19px; text-align: center;"/><span style="font-family: 仿宋; font-size: 19px; text-align: center;">&nbsp;</span><img src="/uploads/20240109/96615f5cc7859aa329581e0b1684b75a.jpg" alt="图片4.jpg" style="font-family: 仿宋; font-size: 19px; text-align: center;"/></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:37px;padding:0 0 0 0 ;text-autospace:ideograph-numeric;line-height:33px"><span style=";font-family:仿宋;font-size:19px">&nbsp;</span></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:37px;padding:0 0 0 0 ;text-autospace:ideograph-numeric;line-height:33px"><span style=";font-family:仿宋;font-size:19px">&nbsp;</span><span style="font-family: 仿宋; font-size: 19px;">&nbsp; &nbsp; &nbsp;&nbsp;</span></p><p><br/></p><p style="text-indent: 37px; text-align: right; line-height: 150%;"><span style=";font-family:仿宋;line-height:150%;font-size:19px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
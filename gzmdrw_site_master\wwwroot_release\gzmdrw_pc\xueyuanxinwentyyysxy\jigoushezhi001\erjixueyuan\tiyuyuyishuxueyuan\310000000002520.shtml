<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_新闻摄影讲座</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002520.shtml" class="text-decoration-none text-dark fw-bold">
                                                新闻摄影讲座
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">新闻中心</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2015-05-12</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1985</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><div class="row" align="center" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><h1 style="margin: 0px; padding: 0px; font-size: 20.8px;"><br/></h1></div><div class="row" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><div style="margin: 0cm 15.75pt 0pt; padding: 0px; line-height: 19.5px;"><span style="line-height: 24px; font-size: 12pt;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 为使同学们深入了解新闻摄影的知识，充分培养学生的课外兴趣，营造良好学习氛围，教育与艺术学部于</span><span style="line-height: 24px; font-size: 12pt;">5</span><span style="line-height: 24px; font-size: 12pt;">月</span><span style="line-height: 24px; font-size: 12pt;">12</span><span style="line-height: 24px; font-size: 12pt;">日</span><span style="line-height: 24px; font-size: 12pt;">下午</span><span style="line-height: 24px; font-size: 12pt;">4</span><span style="line-height: 24px; font-size: 12pt;">点</span><span style="line-height: 24px; font-size: 12pt;">30</span><span style="line-height: 24px; font-size: 12pt;">分在体育楼</span><span style="line-height: 24px; font-size: 12pt;">401</span><span style="line-height: 24px; font-size: 12pt;">举行了题为《摄影简谈》的精彩讲座，我学部学生积极踊跃的到现场悉心聆听。</span><span style="line-height: 24px; font-size: 12pt;"><br/></span></div>&nbsp;<div style="margin: 0cm 15.75pt 0pt; padding: 0px; line-height: 19.5px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span style="line-height: 24px; font-size: 12pt;">此次讲座我们很荣幸地邀请到了现就职于贵州日报新闻采访中心副主任、记者、贵州省首席摄影记者蒲学光老师为大家进行专题讲解。此外，我学部主任兼党总支书记雷帮齐老师，党总支副书记王雪老师也参加了本次讲座。<br/></span></div><div style="margin: 0cm 15.75pt 0pt; padding: 0px; text-align: center; line-height: 19.5px;"><span style="line-height: 24px; font-size: 12pt;"><br/><img src="/uploads/20231021/5d871b6eb3b7fe379f2c39d1bd74ad5e.png" alt="image.png"/></span></div><div style="margin: 0cm 15.75pt 0pt; padding: 0px; line-height: 19.5px; text-indent: 24pt;"><span style="line-height: 24px; font-size: 12pt;">讲座期间，首先由雷帮齐老师对本次讲座的主题进行简介，随后在同学们热烈的掌声中，欢迎请蒲学光老师讲座正式开始。蒲学光老师通过多媒体播放配合讲解的方式，生动地讲解了新闻摄影是什么？新闻摄影及日常摄影的重点是什么？在讲解新闻摄影主要表现时，蒲老师结合大量实践深入浅出的讲解了相关专业知识，讲座现场气氛活跃。<br/></span></div><div style="margin: 0cm 15.75pt 0pt; padding: 0px; text-align: center; line-height: 19.5px; text-indent: 24pt;"><span style="line-height: 24px; font-size: 12pt;"><br/><img src="/uploads/20231021/b9b338aa6d4b7abcee4ac93c4e0f5ff4.png" alt="image.png"/></span></div><div align="left" style="margin: 0cm 15.75pt 0pt; padding: 0px; line-height: 19.5px; text-indent: 24pt;"><span style="line-height: 24px; font-size: 12pt;">本次讲座的开展让同学们进一步了解了新闻摄影的发展，在一定程度上提高了同学们对新闻摄影知识的认知，同学们受益匪浅。</span></div></div><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
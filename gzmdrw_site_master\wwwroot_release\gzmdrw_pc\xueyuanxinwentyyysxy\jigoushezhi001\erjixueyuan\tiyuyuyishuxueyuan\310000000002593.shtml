<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_展望未来 不负韶华 我校体育与艺术学院开展校企合作新生教育活动会议</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002593.shtml" class="text-decoration-none text-dark fw-bold">
                                                展望未来 不负韶华 我校体育与艺术学院开展校企合作新生教育活动会议
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2020-09-28</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2098</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px">2020年9月28日10：30分，我校体育与艺术学院2020级动画专业于大学城校区与贵阳君子谦行科技传媒有限责任公司开展校企合作新生入学教育活动会议。我校副校长左丹、贵阳君子谦行科技传媒有限责任公司执行董事兼总经理徐小雯、导演徐晨寅以及我校2020级动画专业全体新生参加此次新生教育活动会议，会议由我校体育与艺术学院2020级动画专业班主任钟景绣主持。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px;text-align: center"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><img src="/uploads/20231021/2b960f6c640951d65c18c51061cab50a.jpg" data-catchresult="img_catchSuccess"/></span>&nbsp;</p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 24px;text-align: center;line-height: 19.5px"><strong><span style="font-family: 仿宋_GB2312;line-height: 18px;font-size: 12px"><span style="font-family:仿宋_GB2312">我校体育与艺术学院</span>2020级动画专业班主任钟景绣主持新生教育活动会议现场</span></strong></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><span style="font-family:仿宋">会上，贵阳君子谦行科技传媒有限责任公司执行董事兼总经理徐小雯代表贵阳君子谦行科技传媒有限责任公司欢迎我校</span>2020级动画专业本科新生加入到动画行业大集体中。徐小雯表示，希望我校2020级动画专业新生能够怀揣梦想，勤学苦练，珍惜人生中的美好时光，努力提高自己的专业能力，争做动画行业未来的优秀动画人。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px;text-align: center"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><img src="/uploads/20231021/51b5af0ee02604ca6dcf0d404ef14558.jpg" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 24px;text-align: center;line-height: 19.5px"><strong><span style="font-family: 仿宋_GB2312;line-height: 18px;font-size: 12px">贵阳君子谦行科技传媒有限责任公司执行董事兼总经理徐小雯致辞</span></strong></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><span style="font-family:仿宋">会上，贵阳君子谦行科技传媒有限责任公司导演徐晨寅带领我校</span>2020级动画专业新生对动画专业作画类课程的要点做了深入了解，并为我校2020级动画专业新生展示了动画行业的精彩案例，希望我校2020级动画专业新生通过精彩的案例提前认识到自己未来四年的学习生活，鼓励我校2020级动画专业新生努力求学并规划自己的人生。随后，贵阳君子谦行科技传媒有限责任公司各部门负责人分别介绍了各部门所负责课程的未来规划。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px;text-align: center"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><img src="/uploads/20231021/66f5357cea79e88c227189ecacace7d1.jpg" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center;line-height: 19.5px"><strong><span style="font-family: 仿宋_GB2312;line-height: 18px;font-size: 12px">贵阳君子谦行科技传媒有限责任公司导演徐晨寅参加会议并讲话</span></strong></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><span style="font-family:仿宋">最后，我校副校长左丹作总结发言，左丹表示，希望我校</span>2020级动画专业新生在大学期间努力学习，追求更高的人生目标，珍惜校企合作打造的专业平台，踏实学习，夯实基础，提高自身的自律性，摆脱“毕业即失业”的魔咒，为我国动画行业的发展注入优质的新鲜血液。希望我校2020级动画专业新生在我校专业教师的带领下，积极进取，投身于我国动画行业的大家庭中。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px;text-align: center"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><img src="/uploads/20231021/ccc399b7f537938583ef9801d47336d8.jpg" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 19.5px;text-align: center"><span style="font-family: 仿宋;line-height: 28px;font-size: 19px"><img src="/uploads/20231021/a1b2b3f760b4d5d47da1bd95a943b949.jpg" data-catchresult="img_catchSuccess"/></span>&nbsp;</p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><strong><span style="font-family: 仿宋_GB2312;font-size: 12px"><span style="font-family:仿宋_GB2312">左丹副校长出席我校</span>2020级动画专业新生教育活动会议并作总结发言</span></strong></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_规范摆放车辆 共创文明校园</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002226.shtml" class="text-decoration-none text-dark fw-bold">
                                                规范摆放车辆 共创文明校园
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">教育与艺术学部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-05-11</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>3255</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋_GB2312;font-size: 19px">为充分调动学生参加劳动的积极性，营造整洁、规范、文明的校园环境。</span><span style="font-family: 仿宋_GB2312;font-size: 19px">5月10日下午，体育与艺术学院</span><span style="font-family: 仿宋_GB2312;font-size: 19px">党总支组织</span><span style="font-family: 仿宋_GB2312;font-size: 19px"><span style="font-family:仿宋_GB2312">开展</span><span style="font-family:仿宋_GB2312">“规范摆放车辆</span></span><span style="font-family: 仿宋_GB2312;font-size: 19px">&nbsp;&nbsp;</span><span style="font-family: 仿宋_GB2312;font-size: 19px"><span style="font-family:仿宋_GB2312">共创文明校园</span><span style="font-family:仿宋_GB2312">”实践活动</span></span><span style="font-family: 仿宋_GB2312;font-size: 19px">，</span><span style="font-family: 仿宋_GB2312;font-size: 19px"><span style="font-family:仿宋_GB2312">学院第</span><span style="font-family:仿宋_GB2312">3期发展对象培训班小组全体成员参加活动。<br type="_moz"/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px;text-align: center"><img src="/uploads/20231021/a96a215389004bd315c0c8d72d5884bc.png" data-catchresult="img_catchSuccess"/></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; text-align: center;"><span style="box-sizing: border-box;font-weight: 700;color: #333333;font-family: &#39;Helvetica Neue&#39;, Helvetica, Arial, sans-serif;text-indent: 0px"><span style="box-sizing: border-box;font-family: 宋体;letter-spacing: 0;font-size: 16px">打扫停车区域卫生</span></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; text-align: center;"><span style="box-sizing: border-box;font-weight: 700;color: #333333;font-family: &#39;Helvetica Neue&#39;, Helvetica, Arial, sans-serif;text-indent: 0px"><span style="box-sizing: border-box;font-family: 宋体;letter-spacing: 0;font-size: 16px"><img src="/uploads/20231021/f0d1eee4f1de34fa1125b885c8f09103.png" alt="image.png"/><br/>按要求排放车辆<br/>&nbsp;&nbsp;<img src="/uploads/20231021/7f569a76d5a04fcc5fe5fd2e858cf3eb.png" alt="image.png"/></span></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; text-align: center;"><span style="box-sizing: border-box;font-weight: 700;color: #333333;font-family: &#39;Helvetica Neue&#39;, Helvetica, Arial, sans-serif;text-indent: 0px"><span style="box-sizing: border-box;font-family: 宋体;letter-spacing: 0;font-size: 16px">整齐摆放后的停车区域</span></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: "><span style="font-family: 仿宋_GB2312;font-size: 19px">活动中，</span><span style="font-family: 仿宋_GB2312;font-size: 19px"><span style="font-family:仿宋_GB2312">大家前往大学城校区师德楼和四栋宿舍停车区域，分别对两个区域的电动车、自行车、摩托车按照</span><span style="font-family:仿宋_GB2312">“入位、顺向、整齐”的要求</span></span><span style="font-family: 仿宋_GB2312;font-size: 19px">进行规范摆放，并及时联系学校保卫处对废旧车辆进行整理。</span><span style="font-family: 仿宋_GB2312;font-size: 19px"><span style="font-family:仿宋_GB2312">整理结束后，大家在四栋宿舍门口悬挂</span><span style="font-family:仿宋_GB2312">“车辆有序停放，你我出行通畅，共创文明校园”宣传横幅，提醒学校师生有序停放车辆，增强大家规范停车、维护校园整洁的意识与责任感。<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px;text-align: center"><img src="/uploads/20231021/486a9dd97f135ac656312405b3359809.png" alt="image.png"/></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; text-align: center;"><span style="box-sizing: border-box;font-weight: 700;color: #333333;font-family: &#39;Helvetica Neue&#39;, Helvetica, Arial, sans-serif;text-indent: 0px"><span style="box-sizing: border-box;font-family: 宋体;letter-spacing: 0;font-size: 16px">活动合影</span></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
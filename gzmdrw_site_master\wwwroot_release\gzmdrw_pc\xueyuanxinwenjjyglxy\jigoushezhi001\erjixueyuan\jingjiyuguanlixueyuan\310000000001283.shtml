<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_经济与管理学院第一届微团课大赛圆满落幕</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000001283.shtml" class="text-decoration-none text-dark fw-bold">
                                                经济与管理学院第一届微团课大赛圆满落幕
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">经济与管理学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-11-03</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2237</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">为深入贯彻落实习近平总书记关于青年工作的重要思想，坚持以习近平新时代中国特色社会主义思想为指导。进一步夯实基层团支部建设，引导青年团员坚定不移跟党走、奋进新征程，努力在青年团员中营造昂扬向上、团结奋进的浓厚氛围，激发青年爱国、爱党、爱社会主义的情感。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">11月2日14:00，由经济与管理学院党总支主办，经济与管理学院团总支承办的“贵阳人文科学学院经济与管理学院第一届微团课大赛”在自信楼107路演厅成功举办。经过初赛激烈的角逐，共有十二支队伍进入决赛。本次决赛邀请校团委书记田仁贵，经济与管理学院党总支副书记田丹、吕渊，经济与管理学院辅导员唐卿、罗鹃担任评委。由2020级行政管理1班团支部张婷婷主持。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/623ac91e01bdc0a8538260933f4a9de4.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图一 2020级行政管理1班团支部张婷婷主持</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">决赛伊始，由经济与管理学院田丹副书记致辞，她提到学院组织微团课大赛有利于促进学院团支部组织建设和规范管理，进一步引导广大团员青年坚定理想信念，牢记初心使命，提升广大团员青年的责任感、使命感和荣誉感具有重要意义。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/f89de8719769d181ba170b616d58a27b.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图二 经济与管理学院田丹副书记致辞</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">决赛伊始，由经济与管理学院田丹副书记致辞，她提到学院组织微团课大赛有利于促进学院团支部组织建设和规范管理，进一步引导广大团员青年坚定理想信念，牢记初心使命，提升广大团员青年的责任感、使命感和荣誉感具有重要意义。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/983ca446fab062a80408b9a4f8ea4101.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图三 部分参赛选手授课现场</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">校团委田仁贵书记从微团课选题、内容和结构、课件PPT设计、仪态等进行点评。对参赛选手的作品素材收集、讲解词撰写、课件制作等给予肯定。同时，他呼吁青年学子应向革命先辈学习，紧跟党走，拥护党的领导，以最好的状态投身学习工作，回馈人民，回馈社会！做一个新时代的好青年！</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/7a19ea8c144ba8c8aa095435939013a8.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图四 评委点评</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">经过激烈角逐，2021级财务管理2班团支部吴玉洁荣获一等奖；2021级旅游管理1班团支部刘恋、2020级房地产开发与管理1班团支部冉佳伟荣获二等奖；2022级人力资源管理1班团支部韩茂林、2021级旅游管理1班团支部杜培群、2021级旅游管理2班团支部严婷婷荣获三等奖；2021级财务管理1班团支部岑娜琴、2020级电子商务2班团支部陈伦艳荣获优秀奖，2021级财务管理2班团支部、2020级人力资源管理1班团支部优秀组织奖。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/a51c8616c0e6923094f38122f4b066cc.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图五 颁奖现场</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/ce61da907261612cefa562208c7a93a5.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图l六 颁奖现场</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">本次微团课大赛，为青年团员搭建了展示自我、提升能力、交流思想的平台，加深了青年团员对百年党史、团的理解和认识，增强了同学们的使命感和责任感，营造了学习党史、团史等的浓厚氛围。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/af29c67ff45fcd1bf283d6922c613c6c.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图七 决赛合影</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_我院动画专业稳步推进校企合作 产教融合再创新高</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000000979.shtml" class="text-decoration-none text-dark fw-bold">
                                                我院动画专业稳步推进校企合作 产教融合再创新高
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-10-09</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2279</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p data-block-key="fm2wd" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">一、育人先育己</p><p data-block-key="3b1nk" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">立足于服务贵州数字媒体产业发展需要，把握具有德智体美劳全面发展，能掌握动画专业知识、动画创作、制作的专业型人才培养方向，动画专业于2020年09月开启与贵阳君子谦行科技传媒有限责任公司的校企合作办学模式。两年多以来在学校领导的关心与支持下，在学院和企业的共同努力下，动画专业坚持以提升专业教学质量为核心，深化教学理论与教学实践的结合，已逐步形成了强有力的教师团队，学生积极向上的良好学习风气。</p><p data-block-key="6dliv" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">徐小雯老师绘制丛书《氹氹黔游记之中国天眼》入选中宣部“奋进新征程建功新时代”8月推荐书目，并输出阿拉伯国家。</p><p data-block-key="fqg81" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">教师团队制作的《阳明先生传奇》入选国家广播电视总局2022年中国经典民间故事动漫创作工程（网络动画片）重点扶持项目，全国共有10个项目入选，此项目是全省唯一入选项目。</p><p data-block-key="9pleq" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">二、学子风采</p><p data-block-key="ashfl" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">2018级学生廖菲、王鑫，2020级学生覃练龙参与动画制作工作的国产动画《龙族》，目前正在腾讯视频平台火热播出中，全网粉丝超1500万。</p><p data-block-key="egrh3" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">2018级学生王鑫，2020级学生覃练龙、张脯、郑明艳参与动画制作工作的国产知名动画《苍兰诀》，该片自上线以来独占爱奇艺恋爱动漫榜榜首，单集讨论数超10万。</p><p data-block-key="fngnr" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">2018级学生廖菲、王鑫，2020级学生覃练龙、张脯、郑明艳、李志娟、陈富欢、谢欣怡、韦仕榜参与动画制作工作的日本知名游戏改编动画《圣剑传说 玛娜传奇》，目前正在日本电视台火热播出中。</p><p data-block-key="7q5us" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">在学院和企业的共同能力下，动画专业将深入贯彻落实习近平总书记在党的二十大当中提出的“深入实现人才强国战略”，以动画、漫画及数字内容创作、生产、传播及相关支撑技术应用为主要研究内容，以培养创新创业应用型人才为目标，结合全国及贵州动画市场的发展而积极努力。</p><p data-block-key="8k3j8" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: &quot;Open Sans&quot;, -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;; font-size: 19.2px; text-wrap: wrap; background-color: rgb(252, 252, 252);">由于动画专业出色的教学成果，先后有贵州电视台、贵州日报、贵阳网、动静时空、知知贵阳等多家媒体采访报道，展示动画专业人才培养的工作成果。</p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
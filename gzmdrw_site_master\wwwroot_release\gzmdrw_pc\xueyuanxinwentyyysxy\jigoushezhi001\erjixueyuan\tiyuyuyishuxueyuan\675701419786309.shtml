<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院 2025 届视觉传达设计、环境设计、动画专业毕业设计展</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/675701419786309.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院 2025 届视觉传达设计、环境设计、动画专业毕业设计展
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-05-13</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="text-indent: 37px; margin-bottom: 5px; line-height: 2em; margin-top: 5px;"><span style="font-family: Calibri;font-size: 19px">2025 <span style="font-family:宋体">年</span><span style="font-family:Calibri">5</span><span style="font-family:宋体">月</span><span style="font-family:Calibri">12</span><span style="font-family:宋体">日，贵阳人文科技学院体育与艺术学院</span><span style="font-family:Calibri">2025 </span><span style="font-family:宋体">届视觉传达设计、环境设计、动画专业毕业设计展于师德楼</span><span style="font-family:Calibri">2-3</span><span style="font-family:宋体">楼正式启幕。本次展览将持续至</span><span style="font-family:Calibri">5</span><span style="font-family:宋体">月</span><span style="font-family:Calibri">16</span><span style="font-family:宋体">日，体育与艺术学院副院长赵金亮、视觉传达设计教研室主任唐黎、环境设计教研室主任李尧，以及相关专业师生共同出席开幕式。</span></span></p><p style="text-indent: 37px; margin-bottom: 5px; line-height: 2em; margin-top: 5px;"><span style="font-family: Calibri;font-size: 19px"><span style="font-family:宋体"></span></span></p><p style="text-align: center;"><span style="text-indent: 37px;"></span></p><p style="text-align: center;"><img src="/resources/image/2025/05/13/675702187630661.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-align: center;"><span style="text-indent: 37px;"></span><br/></p><p style="text-indent: 37px; margin-bottom: 5px; line-height: 2em; margin-top: 5px;"><span style="font-family: Calibri;font-size: 19px"><span style="font-family:宋体">本届毕业设计展以</span> &quot;<span style="font-family:宋体">创意融合・艺术赋能</span><span style="font-family:Calibri">&quot; </span><span style="font-family:宋体">为主题，全方位呈现学生四年专业学习的丰硕成果。视觉传达设计专业作品聚焦海报设计、品牌形象塑造、包装创意等领域，巧妙融合传统与现代元素，将艺术审美与商业需求有机结合，充分展现学生对视觉语言的深刻理解与灵活运用；环境设计专业作品涵盖室内空间规划、景观设计等方向，通过对空间功能与美学的深度思考，体现出学生对人居环境的敏锐洞察，以及对理想生活图景的不懈追求；动画专业的系列短片作品，从故事架构到视觉特效，皆彰显出创作者的创新思维与扎实的艺术功底。</span></span></p><p style="text-indent: 37px; margin-bottom: 5px; line-height: 2em; margin-top: 5px;"><span style="font-family: Calibri;font-size: 19px"><span style="font-family:宋体">开幕式上，赵金亮副院长对学生们展现出的创新意识与专业能力给予高度认可，鼓励学子们持续保持探索热情，以专业所学服务社会发展，同时对指导教师的悉心指导表示衷心感谢。唐黎、李尧两位主任分别从专业视角对学生作品进行深入点评，在肯定创作亮点的同时，也针对存在的不足提出改进建议，期望同学们在总结经验中不断精进专业技艺。</span></span></p><p style="text-indent: 37px; margin-bottom: 5px; line-height: 2em; margin-top: 5px;"><span style="font-family: 宋体; font-size: 19px;">此次毕业设计展不仅是学生专业能力的集中展示，也为各专业师生交流搭建了平台，为跨学科交流、学生学业进步、推动专业发展提供了契机。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
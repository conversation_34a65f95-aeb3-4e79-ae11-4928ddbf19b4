<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_“奋进新征程，谱写青春华章--庆祝党的二十大胜利召开美术·设计作品展”开幕</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002252.shtml" class="text-decoration-none text-dark fw-bold">
                                                “奋进新征程，谱写青春华章--庆祝党的二十大胜利召开美术·设计作品展”开幕
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-12-14</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2807</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px">2022年12月13日下午，由体育与艺术学院党总支主办的 “奋进新征程，谱写青春华章——庆祝党的二十大胜利召开美术·设计作品展” 在大学城校区开幕。</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px">此次展览开幕式由体育与艺术学院党总支书记王雪主持，校党委副书记马帅，体育与艺术学院院长雷帮齐，组织人事部副部长徐楠云，体育与艺术学院副院长车莹莹，体育与艺术学院党总支副书记邓程民，组织部老师唐林鹏、汤雷、潘琦以及体育与艺术学院党总支委员赵金亮、刘加坤等领导嘉宾和老师出席了本次开幕式。<br/></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: &quot;Microsoft YaHei&quot;; font-size: 13px; text-wrap: wrap; background-color: rgb(255, 255, 255); text-indent: 43px; line-height: 37px; text-align: center;"><span style="font-family: 仿宋_GB2312;font-size: 21px"><img src="/uploads/20231021/9b3daae5be7557e2f062e6c997144dbf.png" alt="image.png"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-size: 14px">开幕式合影</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px">本次展览旨在庆祝党的二十大胜利召开，践行举旗帜、聚民心、育新人、兴文化、展形象的使命任务，积极弘扬中华优秀文化和艺术精神，展现新时代青年大学生的精神风貌。<br/></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; font-family: &quot;Microsoft YaHei&quot;; font-size: 13px; text-wrap: wrap; background-color: rgb(255, 255, 255); text-indent: 43px; line-height: 37px; text-align: center;"><span style="font-family: 仿宋_GB2312;font-size: 21px"><img src="/uploads/20231021/118d2550982c3ce09f42b9b56c0295fc.png" alt="image.png"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体"><span style="font-size: 14px">雷帮齐院长致辞</span></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px">雷帮齐院长在致辞中肯定了广大师生的辛勤付出，鼓励广大青年大学生在这个新的发展时期更好地肩负起历史的使命，争取取得更大的进步。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;font-size: 21px"><img src="/uploads/20231021/71e050a25f2ba6ae525c46a7b5f9c96d.png" alt="image.png"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体"><span style="font-size: 14px">校党委副书记马帅宣布展览开幕</span></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">随后，校党委副书记马帅同志在全场热烈的掌声中宣布：奋进新征程，谱写青春华章</span><span style="font-family:仿宋_GB2312">——庆祝党的二十大胜利召开美术·设计作品展开幕！</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px"><span style="font-family: 仿宋_GB2312;font-size: 21px"><span style="font-family:仿宋_GB2312">本次美术</span><span style="font-family:仿宋_GB2312">·设计作品展，共收到报送作品156件，经过两轮严格评审，展出中国画、油画、版画、插画设计、海报设计等精品共计50余件。<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;font-size: 21px"><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><img src="/uploads/20231021/aebb28b58ab47fe6b75c9e257f172dbb.png" data-catchresult="img_catchSuccess"/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-size: 14px"><span style="font-family: 黑体">展览现场<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><img src="/uploads/20231021/3eb45519b6ba2b1baedfd857b6b016ed.png" data-catchresult="img_catchSuccess"/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-size: 14px"><span style="font-family: 黑体">展览现场<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 27px">部分入选作品<br/><img src="/uploads/20231021/441c5b91cd6de894e6de8c98e4362f94.png" width="600" height="398" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">《黔乡飞瀑》</span>&nbsp;</span><span style="font-family: 黑体;font-size: 19px">&nbsp;<span style="font-family:黑体">中国画</span></span>&nbsp;</p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">(2020级美术学专业国画班 毛宵)<br/><br/><img src="/uploads/20231021/adc5576bcbf4e2ef62132cb98aec0cd5.png" width="600" height="876" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《云</span><span style="font-family: 黑体;font-size: 19px">山</span><span style="font-family: 黑体;font-size: 19px">图》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;<span style="font-family:黑体">中国画</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">(</span><span style="font-family: 黑体;font-size: 19px">2020级美术学专业国画班 李兴巧</span><span style="font-family: 黑体;font-size: 19px">)<br/><br/><img src="/uploads/20231021/2d4484b7cc15def9259c147c77a48f88.png" width="600" height="473" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">《印</span><span style="font-family:黑体">·迹》</span></span><span style="font-family: 黑体;font-size: 19px">&nbsp;</span><span style="font-family: 黑体;font-size: 19px">&nbsp;<span style="font-family:黑体">综合材料</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">(2019级美术学专业综合班 陈慧)<br/><br/><img src="/uploads/20231021/e46dc4173f5606bd0c8ba38af401af9c.png" width="600" height="739" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">《迎</span><span style="font-family:黑体">5G》 &nbsp;丙烯画</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">(2021级美术学专业油画班 王翔)<br/><br/><img src="/uploads/20231021/2ac1ada49c1f75ba8649324bb0c9adc3.png" width="600" height="805" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《</span><span style="font-family: 黑体;font-size: 19px">港</span><span style="font-family: 黑体;font-size: 19px">》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;</span><span style="font-family: 黑体;font-size: 19px">版画</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span><span style="font-family:黑体">2020级美术学专业综合班 陈悦）<br/><img src="/uploads/20231021/7300ceb55aa4d6a4e5874be77f2c0c2e.png" width="600" height="803" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《守卫娄山关》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;</span><span style="font-family: 黑体;font-size: 19px">版画</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span>&nbsp;<span style="font-family:黑体">2019级美术学专业综合班 方毅龙）<br/><img src="/uploads/20231021/3d17b535572c20b181f8d060b9194d9f.png" width="600" height="848" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《</span><span style="font-family: 黑体;font-size: 19px">众志成城，贵阳加油</span><span style="font-family: 黑体;font-size: 19px">》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;</span><span style="font-family: 黑体;font-size: 19px">海报设计</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span><span style="font-family:黑体">2020级动画班 岳应红）<br/><br/><img src="/uploads/20231021/22bab3a2cf4642ee640af9af5c499923.png" width="600" height="849" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">《戏</span><span style="font-family:黑体">·宇，中国》&nbsp;</span></span><span style="font-family: 黑体;font-size: 19px">&nbsp;</span><span style="font-family: 黑体;font-size: 19px">插画设计</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span><span style="font-family:黑体">2020级美术学专业综合班 文江洪）<br/><br/><img src="/uploads/20231021/939448e12c6203278d1f497077b8fc4d.png" width="600" height="433" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《</span><span style="font-family: 黑体;font-size: 19px">喜迎二十大</span><span style="font-family: 黑体;font-size: 19px">》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;<span style="font-family:黑体">插画</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><br/></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span><span style="font-family:黑体">2021级美术学专业油画班 殷小双）<br/><br/><img src="/uploads/20231021/99706e84f4ac60102552517f3b2dc4ee.png" width="600" height="846" alt="" style="border: 0px" data-catchresult="img_catchSuccess"/><br/><br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px">《喜迎二十大》</span><span style="font-family: 黑体;font-size: 19px">&nbsp;&nbsp;</span><span style="font-family: 黑体;font-size: 19px">海报设计</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-align: center"><span style="font-family: 黑体;font-size: 19px"><span style="font-family:黑体">（</span><span style="font-family:黑体">2021级视觉传达班 王庭安）</span></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
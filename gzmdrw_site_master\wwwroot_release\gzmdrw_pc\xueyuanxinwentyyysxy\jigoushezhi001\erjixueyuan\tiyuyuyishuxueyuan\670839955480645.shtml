<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_贵州众誉汇酒业有限公司与我院视觉传达设计教研室校企合作启动</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/670839955480645.shtml" class="text-decoration-none text-dark fw-bold">
                                                贵州众誉汇酒业有限公司与我院视觉传达设计教研室校企合作启动
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-04-28</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="text-indent: 28px; line-height: 2em; text-align: justify; margin-top: 15px;"><span style="font-family: 宋体; font-size: 20px;"><span style="font-family: Calibri;">&nbsp; 2025 </span><span style="font-family: 宋体;">年 </span><span style="font-family: Calibri;">4 </span><span style="font-family: 宋体;">月 </span><span style="font-family: Calibri;">25 </span><span style="font-family: 宋体;">日，在贵州省贵阳市金阳中华桥全国运营中心，我院视觉传达设计教研室与贵州众誉汇酒业有限公司进行校企合作的签约，贵州众誉汇酒业有限公司是一家专注于酒类生产、运营、推广的企业，公司主营“中华桥”品牌，该公司致力于成为酒类行业中具有创新性、高品质和独特品牌形象的企业。参会人员有贵州众誉汇酒业有限公司总经理徐章，我院视觉传达设计教研室主任唐黎及专职教师张胜佳。在洽谈中，了解到该企业主要与我院视觉传达设计专业对口的岗位有文案策划、新媒体运营、平面设计师等。</span>合作启动仪式隆重举行并取得圆满成功。此次合作标志着双方在人才培养、实践创新等领域迈入了新的发展阶段，开启了强强联合、共育英才的崭新篇章。</span></p><p style="text-indent: 28px; line-height: 2em;"><span style="font-family: 宋体; font-size: 20px;"></span></p><p style="text-align: center;"><img src="/resources/image/2025/04/29/670840143138885.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-indent: 28px; line-height: 2em; text-align: center;"><strong>签约现场</strong></p><p style="text-indent: 28px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体; font-size: 20px;">在签约仪式上，双方秉持着优势互补、资源共享、协同发展的理念，正式达成合作协议。此次合作以校企融合为纽带，以实践创新为引擎，致力于深化“校企合作、实现多赢”的目标。双方将在多个方面展开深度合作：共建实训高地，为学生提供更贴近实际工作场景的实践平台，提升学生的专业技能和实践能力；建立人才共育机制，共同制定人才培养方案，确保人才培养与企业需求精准对接；搭建资源共享平台，实现教学资源、实训设备、师资力量等资源的共享与优化配置；构建灵活实践体系，为学生提供多样化的实践机会，拓宽学生的职业视野。</span></p><p style="text-indent: 28px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体; font-size: 20px;">此次校企合作的达成，不仅为我院的学生提供了更广阔的发展空间和实践机会，也为中华桥全国运营中心注入了新的活力和创新源泉。双方将携手共进，共同探索校企合作新模式，为培养适应社会需求的高素质人才贡献力量，推动教育与产业的深度融合与发展，为地方经济社会发展做出积极贡献。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_歌唱祖国||体育与艺术学院“喜迎二十大，奋进新征程”</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002308.shtml" class="text-decoration-none text-dark fw-bold">
                                                歌唱祖国||体育与艺术学院“喜迎二十大，奋进新征程”
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-10-05</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2173</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;text-align: justify;line-height: 37px"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><span style="font-family:仿宋_GB2312">历史长河流淌着中华民族的精神血脉，百年伟业承载着生生不息的奋斗追求。为热烈庆祝中华人民共和国成立</span><span style="font-family:仿宋_GB2312">73周年，以更加饱满昂扬的奋斗姿态迎接党的二十大胜利召开</span></span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">2022年10月4日，体育与艺术学院在贵阳人文科技学院大学城校区举办了《歌唱祖国||体育与艺术学院“喜迎二十大，奋进新征程”》的主题活动，体育与艺术学院孟红成和韩沙两位老师组织此次活动，2020级音乐学专业全体学生参加。<br/></span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 活动开始，我院</span><span style="font-family:仿宋_GB2312">2020级音乐学专业全体学生齐声唱到“大道直行迎接新的曙光/你看党旗飘扬的方向/是我要去胜利的地方/千难万险也不能阻挡/冲锋在前我用生命开创/你看党旗飘扬的方向/有我唯一铁打的信仰/全力以赴春天的梦想/大道直行迎接新的曙光”。一首《党旗飘扬的方向》慷慨激昂，悦耳的歌声响彻整个校园。唱出了我院学子对党的崇敬之情和不忘初心，砥砺前行的坚定信念。在歌词中展示了党风雨征途一如既往，使命抗肩永不休，初心不改，使命不渝，精神永恒的风采，也表现了党带领全国人民扬帆起航，追求中华民族伟大复兴的中国梦的伟大精神。</span></span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;text-align: justify;line-height: 37px"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><span style="font-family:仿宋_GB2312">前一首歌声还在耳畔回响，激昂的歌声又响了起来。</span><span style="font-family:仿宋_GB2312">“我们的生活天天向上/我们的前程万丈光芒/五星红旗迎风飘扬/胜利歌声多么响亮/歌唱我们亲爱的祖国/从今走向繁富强”。</span></span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">振奋人心的歌曲唱</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">出</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">了</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">体育与艺术学院的学子们</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">对伟大祖国、伟大中国共产党的无限深情与热爱。在熟悉的旋律中，</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">歌唱者们</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">共同回顾中国共产党发展壮大的光辉历程</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">在振奋的歌声中，参演人员共同展望新时代祖国繁荣富强的锦绣前程。大家用歌声抒发对党的深情、对国家的热爱和</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">对</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">祖国的</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">真挚</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">祝福。</span><br/><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 活动的最后，用一曲《今天是你的生日》唱响对祖国母亲深情祝福。</span><span style="font-family:仿宋_GB2312">“……我们祝福你的生日我的中国/愿你逆风起飞获得收获/我们祝福你的生日我的祖国/这是儿女们心中期望的歌……”。这是体育与艺术学院全体学子对祖国母亲发自内心的祝福和期望。用最响亮的声表达出我们内心的激动和深情。五星红旗的光芒，照耀在祖国大地，成为我们追随的光。“我是中国人！”不只是一句口号，而是包含着我们无数人的自豪之情，挥动手中的五星红旗以青春为祖国献礼。参加活动的全体同学为中国母亲喝彩。青春之少年、青春之中国，我们为您祝福。我们一起奋起直追，不负青春、不负韶华、不负梦想、不负未来。</span></span><br/><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">&nbsp; &nbsp; 此次活动进一步增强了</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">我院</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">师生热爱党、热爱祖国的情感，展示了</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">我院全体学生</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">的风采和昂扬向上的精神风貌，向党和祖国献上最真挚的祝福</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">。</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">也</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">充分展示</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">我院学子</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">昂扬向上的精神风貌和永远听党话、坚定跟党走的信心</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">和</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">决心以及热盼党的二十大胜利召开的喜悦之情。</span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;text-align: justify;line-height: 37px"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">在二十大的顺利召开之际，站在</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px">“</span><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><span style="font-family:仿宋_GB2312">两个一百年奋斗目标</span><span style="font-family:仿宋_GB2312">”的交汇点上，我们应当以朝气与锐气、激情与热情、诚心与恒心让号角声传得更远，让光芒更加耀眼，让旗帜永远鲜红，让“愿以寸心寄华夏，且将岁月赠山河”的信念震耳发聩，让“征程万里云鹏举，笃力奋楫开新篇”的志向气吞山河，在建设新一百年征程上阔步而行。乘百舸争流之势，续中华民族之魂。今日的祖国繁荣昌盛，是中国共产党带领全体人民艰苦奋斗的结果，是中华儿女不畏艰难，奋力搏的成就。党一路带领中国人民披荆斩棘开启了华夏民族的新篇章，向着民族复兴中国梦的宏伟蓝图扬帆启航。<br/></span></span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;text-align: center;line-height: 37px"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><img src="/uploads/20231021/a93fbb194396cdc64524d171f79818bb.png" alt="image.png"/><br/></span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><img src="/uploads/20231021/04f6823dbe6315c1324cc0976f62edb1.png" alt="image.png"/></span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><img src="/uploads/20231021/444ee9181b168515ce4f3191d69751c0.png" alt="image.png"/></span></p><p style="padding: 0px; font-family: &quot;Microsoft YaHei&quot;; font-size: 13px; text-wrap: wrap; background-color: rgb(255, 255, 255); text-indent: 43px; line-height: 37px; text-align: center;"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><img src="/uploads/20231021/2792ca80c5f2d7cad6c24edd7368b71e.png" alt="image.png"/><br/></span></p><p style=";padding: 0;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 43px;line-height: 37px;text-align: center"><span style="font-family: 仿宋_GB2312;letter-spacing: 0;font-size: 21px"><img src="/uploads/20231021/030f876889a062c455977741806f32e5.png" alt="image.png"/></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
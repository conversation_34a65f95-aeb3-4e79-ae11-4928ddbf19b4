<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学院举行2023-2024第二学期首次升旗仪式</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000004009.shtml" class="text-decoration-none text-dark fw-bold">
                                                学院举行2023-2024第二学期首次升旗仪式
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-03-12</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1866</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 43px"><span style="color: #555555; font-family: ">为进一步加强学生爱国主义教育，3月4日、11日，学院分别在大学城校区、花溪校区举行了升旗仪式，学院党总支副书记邓程民、团总支书记支胜杰出席升旗仪式，学院2021级、2022级24个班级共746名学生参加了大学城校区升旗仪式，2023级11个班级420名同学参加花溪校区升旗仪式</span><span style="color: #555555; font-family: ">。</span></p><p style="text-autospace:ideograph-numeric;text-align:center"><img src="/uploads/20240320/b7521c25ffd2ec5098b99fe671585441.jpg" alt="升旗仪式现场.jpg"/><span style="color: #555555; font-family: "></span><span style="color: #555555; font-family: ">&nbsp;升旗仪式现场</span></p><p style="text-indent: 41px"><span style="color: #555555; font-family: ">清晨6点40，各班同学列队完毕，各项准备工作完毕。7点整，国旗护卫队成员迈着整齐有力的步伐护送五星红旗走到升旗台，在庄严的国歌声中五星红旗冉冉升起，现场的师生一起向国旗行注目礼，表达对祖国的热爱和崇敬。</span></p><p style="text-autospace:ideograph-numeric;text-align:center"><img src="/uploads/20240320/ea21f3a2a1b4a420d3d50fb334f25e1b.jpg" alt="国旗护卫队.jpg"/><span style="color: #555555; font-family: ">国旗护卫队护送国旗</span></p><p style="text-indent: 41px"><span style="color: #555555; font-family: ">仪式现场，2021级视觉传达设计专业学生余佳伦、2023级舞蹈表演专业学生郭馨语分别做国旗下的讲话。</span></p><p style="text-align: center; text-indent: 0em;"><img src="/uploads/20240320/ae364fa0b5fad2efbc2cd52abd3cdac1.jpg" alt="余佳伦发表国旗下讲话.jpg" style="float: none; display: inline;"/></p><p style="text-autospace:ideograph-numeric;text-align:center"><span style="color: #555555; font-family: ">余佳伦发表国旗下讲话</span></p><p style="margin-top:9px;margin-right:6px;margin-left:2px;text-indent:43px;text-align:justify;text-justify:inter-ideograph;line-height:129%"><span style="color: #555555; font-family: ">余家伦表示，大学期间积极参加政治学习、社会实践活动，提升理论素养和实践能力，成为有理想、有本领、有担当的新时代青年是在场每一个同学努力的目标，成长从来不是由外界决定，也从来不是由成功和成就的高度来划定的，作为新时代大学生成长是自身多样化和人格完满化的一个基本结果。希望大家都勇敢的去追求梦想，勇于逆境中成长，勇于担当使命，努力成为栋梁之才。</span></p><p style="margin-top:9px;margin-right:6px;text-align:center;line-height:129%"><img src="/uploads/20240320/f0f5dde02552be0951bfe744f73e4dc5.jpg" alt="郭馨语.jpg"/><span style="color: #555555; font-family: ">&nbsp;郭馨语发表国旗下的讲话</span></p><p style="margin-top:9px;margin-right:6px;margin-left:2px;text-indent:43px;text-align:justify;text-justify:inter-ideograph;line-height:129%"><span style="color: #555555; font-family: ">郭馨语表示，青春正当时，奋斗亦当时。她号召学院青年学生不负韶华、牢记使命担当，将自己的奋斗融入于民族的奋斗之中，用我们的青春和智慧，在新时代做有为青年，努力书写新时代的壮丽篇章。</span></p><p style="margin-top:9px;margin-right:6px;text-align:center;line-height:129%"><img src="/uploads/20240320/f428fae9a6d39f179f802d5b48044d64.jpg" alt="邓程民.jpg"/><span style="color: #555555; font-family: ">&nbsp;</span><span style="color: #555555; font-family: ">邓程民做新学期动员讲话</span></p><p style="margin-top:9px;margin-right:6px;margin-left:2px;text-indent:43px;text-align:justify;text-justify:inter-ideograph;line-height:129%"><span style="color: #555555; font-family: ">学院党总支副书记邓程民在升旗仪式上做新学期动员讲话，希望同学们在新学期拥有新的奋斗目标和良好的精神状态，坚持走出课堂，走入校园，走入社会，用慢慢的热情投入到理论学习和专业实践中，找准目标、努力上进，在新学期收获新的成绩。</span></p><p style="margin-top:9px;margin-right:6px;margin-left:2px;text-indent:43px;text-align:justify;text-justify:inter-ideograph;line-height:129%"><span style="color: #555555; font-family: ">本次升旗仪式是新学期学院在两个校区举行的第一次升旗仪式，也是一次全面的爱国主义教育。升旗仪式结束后，学院师生将进一步牢记国家使命感和民族责任感，以崭新的面貌迎接新的工作、学习和生活。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
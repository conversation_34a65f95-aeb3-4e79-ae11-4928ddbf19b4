<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_教育与艺术学部团总支学生会“学雷锋纪念日”活动圆满结束</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002560.shtml" class="text-decoration-none text-dark fw-bold">
                                                教育与艺术学部团总支学生会“学雷锋纪念日”活动圆满结束
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">美术系</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2014-03-11</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1974</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 33px;text-align: justify"><span style="font-size: 19px;font-family: ’仿宋_GB2312’">每一年的3月5日是受人瞩目的“学雷锋纪念日”，在这一天，全国各地的学校会纷纷组织同学们开展各种各样的活动来纪念伟大的英雄雷锋同志，我们教育与艺术学部团总支学生会的同学们也不例外，应贵州民族大学人文科技学院团委学生会下发的任务，我学部也开展了一次向雷锋同志学习，向雷锋同志致敬的活动。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 33px;text-align: justify"><span style="font-size: 19px;font-family: ’仿宋_GB2312’">对于现如今的大学生来说，能够积极动手打扫卫生的同学是越来越少，所以为了响应“学雷锋纪念日”，我学部决定组织一次全民动员大扫除，由学部负责老师带领同学们打扫学部的每一个教室、走廊，此次活动为期一个星期，每一天都有分配不同的学生负责打扫，做到让每一位同学都能够得到锻炼。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 33px;text-align: justify"><span style="font-size: 19px;font-family: ’仿宋_GB2312’">此次活动的任务一下发，学部的同学们都能够积极主动参加，大家齐心协力、不分专业、不分班级一起努力的打扫卫生，把各个教室以及厕所都打扫得干干净净。任务完成后，虽然每一位同学都累得满身大汗，但是都觉得得到了充实。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 33px;text-align: justify"><span style="font-size: 19px;font-family: ’仿宋_GB2312’">为期一个星期的“学雷锋纪念日”活动圆满的结束了，虽然这是一次带有活动性质的大扫除，但同学们还是完成得非常开心，通过这一次活动的展开，让教育与艺术学部的同学们更加紧密的联系在了一起，大家都做到了积极动手，把这一次的活动完成得非常圆满，这一次活动的圆满结束也标志着同学们对伟大英雄雷锋同志的深切怀恋，也让同学们在锻炼中得到成长，相信在今后的学习、生活中，同学们都能够更上一层楼，能够变得更加成熟。<br/></span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 33px;text-align: center"><span style="font-size: 19px;font-family: ’仿宋_GB2312’"><img src="/uploads/20231021/953d89952c69815d4ebd1caef9eb2c6f.png" alt="image.png"/></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; text-indent: 37px; line-height: 33px; text-align: center;"><span style="font-size: 19px;font-family: ’仿宋_GB2312’">学部学生打扫教室卫生<br/><img src="/uploads/20231021/6ef7ec67c02b86909a4ae70fdf10d78b.png" alt="image.png"/></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
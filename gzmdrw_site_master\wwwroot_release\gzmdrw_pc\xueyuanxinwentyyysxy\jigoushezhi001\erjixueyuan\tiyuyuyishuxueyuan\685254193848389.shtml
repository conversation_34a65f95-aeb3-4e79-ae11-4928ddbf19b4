<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_油画里的西方浪漫｜2025届美术学专业毕业生油画作品展</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/tongzhigonggaotyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/685254193848389.shtml" class="text-decoration-none text-dark fw-bold">
                                                油画里的西方浪漫｜2025届美术学专业毕业生油画作品展
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-06-02</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="text-indent: 2em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">时维乙巳初夏，2025届美术学毕业生以丹青为媒，集四年学业之粹，汇成此青春画卷。笔墨纵横处，见少年意气，藏天地乾坤。国画之韵、油画之彩，皆承千年文脉而发新声。此展既呈山川之壮阔，复现人文之精微。或见苗岭晨曦染绢素，或观侗寨炊烟入油彩。起承转合间，有传统法度，虚实浓淡处，见时代精神。四载寒窗凝于此展，千般匠心俱在方寸。非独技艺之展示，实为心灵之独白。</span></p><p style="text-indent: 2em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">此次展览共展出毕业创作58幅（组），其中国画30幅（组）；油画28幅（组）。</span></p><section style="-webkit-tap-highlight-color: transparent; margin: 0px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); visibility: visible; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; visibility: visible;"><section style="-webkit-tap-highlight-color: transparent; margin: 35px 0px 10px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; justify-content: center; display: flex; flex-flow: row; visibility: visible;"><section data-lazy-bgimg="https://mmbiz.qpic.cn/sz_mmbiz_png/MVPvEL7Qg0GUbibFb0kLia72GAI2Ha6LvIlSibCicloqxMONHum16dKFiccrcuB4S20WiannGjvT4ntYkaZw3CdbbR5g/640?wx_fmt=png" class="" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 7px 51px 7px 24px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: top; width: auto; align-self: flex-start; flex: 0 0 auto; background-repeat: no-repeat; background-attachment: scroll; min-width: 5%; height: auto; background-image: url(&quot;https://mmbiz.qpic.cn/sz_mmbiz_png/MVPvEL7Qg0GUbibFb0kLia72GAI2Ha6LvIlSibCicloqxMONHum16dKFiccrcuB4S20WiannGjvT4ntYkaZw3CdbbR5g/640?wx_fmt=png&amp;tp=webp&amp;wxfrom=15&amp;wx_lazy=1&quot;); background-size: cover !important; background-position: 50% 50%; visibility: visible;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; text-align: justify; color: rgb(255, 255, 255); visibility: visible;"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; visibility: visible; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;">美术学专业2025届毕业生油画作品展代表作品</span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 0px 0px 0px -40px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: top; width: auto; min-width: 5%; flex: 0 0 auto; height: auto; visibility: visible;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: flex; width: 33.8438px; flex-flow: column; visibility: visible;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; z-index: 1; transform: rotateZ(240deg); visibility: visible;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px 0px -2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: left; line-height: 0; visibility: visible;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 30px; height: auto; visibility: visible;"><img class="rich_pages wxw-img" data-ratio="1.564" data-s="300,640" data-w="500" data-src="iurl://resources/image/2025/06/09/685255135072325.png?type=resource&amp;id=685255135072325&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="5" src="/resources/image/2025/06/09/685255145119813.png" _width="100%" alt="图片" data-report-img-idx="1" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 30px !important; visibility: visible !important; max-width: 100%;" /></section></section></section></section></section></section></section></section><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); visibility: visible; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center; visibility: visible;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto; visibility: visible;"><img class="rich_pages wxw-img" data-ratio="1.4462962962962962" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135146053.png?type=resource&amp;id=685255135146053&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="6" src="/resources/image/2025/06/09/685255145177157.png" _width="100%" alt="图片" data-report-img-idx="5" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">刘静 《镜像》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.6666666666666666" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135211589.jpeg?type=resource&amp;id=685255135211589&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="7" src="/resources/image/2025/06/09/685255145234501.jpeg" _width="100%" alt="图片" data-report-img-idx="6" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">权勤熠 《生活的节奏》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.3936170212765957" data-s="300,640" data-w="1034" data-src="iurl://resources/image/2025/06/09/685255135273029.jpeg?type=resource&amp;id=685255135273029&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="8" src="/resources/image/2025/06/09/685255145295941.jpeg" _width="100%" alt="图片" data-report-img-idx="7" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">费贤会 《舒适空间》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.812037037037037" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135367237.jpeg?type=resource&amp;id=685255135367237&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="9" src="/resources/image/2025/06/09/685255145365573.jpeg" _width="100%" alt="图片" data-report-img-idx="8" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: center; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">简贵莲 《欲望》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.7324074074074074" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135424581.jpeg?type=resource&amp;id=685255135424581&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="10" src="/resources/image/2025/06/09/685255145418821.jpeg" _width="100%" alt="图片" data-report-img-idx="9" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">汪俊 《剥落的记忆》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.47685185185185186" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135486021.jpeg?type=resource&amp;id=685255135486021&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="11" src="/resources/image/2025/06/09/685255145480261.jpeg" _width="100%" alt="图片" data-report-img-idx="10" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">揭嘉诚 《外婆的油菜花田》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.7574074074074074" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135543365.jpeg?type=resource&amp;id=685255135543365&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="12" src="/resources/image/2025/06/09/685255145533509.jpeg" _width="100%" alt="图片" data-report-img-idx="11" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">蓝丽芳 《角落碎片》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.3660618996798293" data-s="300,640" data-w="937" data-src="iurl://resources/image/2025/06/09/685255135608901.jpeg?type=resource&amp;id=685255135608901&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="13" src="/resources/image/2025/06/09/685255145594949.jpeg" _width="100%" alt="图片" data-report-img-idx="12" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">聂兴茂 《山里红》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.75" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135670341.jpeg?type=resource&amp;id=685255135670341&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="14" src="/resources/image/2025/06/09/685255145652293.jpeg" _width="100%" alt="图片" data-report-img-idx="13" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">潘梦如 《彩色生活之室内一角》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.6129629629629629" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135735877.jpeg?type=resource&amp;id=685255135735877&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="15" src="/resources/image/2025/06/09/685255145713733.jpeg" _width="100%" alt="图片" data-report-img-idx="14" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">沈睿杰 《尘封的动力》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.5537037037037038" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135793221.jpeg?type=resource&amp;id=685255135793221&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="16" src="/resources/image/2025/06/09/685255145766981.jpeg" _width="100%" alt="图片" data-report-img-idx="15" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">殷小双 《童趣》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.8564814814814815" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135871045.jpeg?type=resource&amp;id=685255135871045&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="17" src="/resources/image/2025/06/09/685255145840709.jpeg" _width="100%" alt="图片" data-report-img-idx="16" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">谢汶涓 《乡土市井》</strong></span></p></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0; text-align: center;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.6694444444444444" data-s="300,640" data-w="1080" data-src="iurl://resources/image/2025/06/09/685255135936581.jpeg?type=resource&amp;id=685255135936581&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="18" src="/resources/image/2025/06/09/685255145902149.jpeg" _width="100%" alt="图片" data-report-img-idx="17" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">王丹 《人群与霓虹的交错》</strong></span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"></strong></span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;"><br/></span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">从调色盘到画布，从临摹到原创</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">四年油画笔触里</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">藏着不为人知的坚持</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">刮刀与画笔交织的日夜</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">终于凝结成眼前斑斓的风景</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;"><br style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;"/></span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; font-family: 宋体, SimSun; box-sizing: border-box !important; overflow-wrap: break-word !important;">四年的坚持与热爱终得绽放</span></p><p style="margin-top: 0px; margin-bottom: 2px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">此去，愿以色彩为舟</strong></span></p><p style="margin-top: 0px; margin-bottom: 0px; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 15px; letter-spacing: 2px; text-align: center; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); -webkit-tap-highlight-color: transparent; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 20px; font-family: 宋体, SimSun;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">在艺术的海洋继续乘风破浪</strong></span></p></section></section></section></section>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
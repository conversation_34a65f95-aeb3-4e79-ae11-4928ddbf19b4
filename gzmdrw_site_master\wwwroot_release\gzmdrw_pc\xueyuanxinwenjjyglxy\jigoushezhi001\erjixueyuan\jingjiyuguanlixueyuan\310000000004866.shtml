<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_三栋楼宇开展“情系人文 砥砺前行"毕业季主题活动</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000004866.shtml" class="text-decoration-none text-dark fw-bold">
                                                三栋楼宇开展“情系人文 砥砺前行"毕业季主题活动
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-29</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1450</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-align: left; line-height: 39px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;<span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">花开满五月，毕业致青春。5月28日，三栋楼宇</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">举办“情系人文</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">&nbsp;</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">砥砺前行”</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">毕业季</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">主题活动，此次活动通过“说出自己想说的话”、为毕业生派发精美礼物等方式，让即将毕业的大四学生感受到学校的温暖，同时</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">增进同学们的互动交流，</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">推动在校大学生情感价值观的健康发展。出席</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">本次</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">活动的有</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">贵阳人文科技学院</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">副校长温小军、经管学院党总支书记田丹、党总支副书记李爽、三栋楼宇副楼长陈晓云以及三栋学生公寓</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">学生代表</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">。本次活动由陈晓云</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">老师</span><span style="text-indent: 43px; font-family: 仿宋; font-size: 21px;">主持。</span></p><p style="text-align: center; line-height: 39px;"><strong style="text-align: center;"><span style="font-family: 方正仿宋_GB2312;font-size: 15px"><img src="/uploads/20240529/9336aa5492f28ca17ac740f46ecfc9d7.png" alt="图片1.png"/></span></strong></p><p style="text-align: center; line-height: 39px;"><strong style="text-align: center;"><span style="font-family: 方正仿宋_GB2312;font-size: 15px">开幕式现场</span></strong></p><p style="text-indent:43px;text-align:justify;text-justify:inter-ideograph"><span style="font-family: 仿宋;font-size: 21px">活动伊始，</span><span style=";font-family:仿宋;font-size:21px">田丹书记</span><span style=";font-family:仿宋;font-size:21px">介绍了举办本次活动的背景及意义，并</span><span style=";font-family:仿宋;font-size:21px">对三栋学生公寓全体同学表达了</span><span style=";font-family:仿宋;font-size:21px">由衷的感谢</span><span style=";font-family:仿宋;font-size:21px">和诚挚的祝福，</span><span style=";font-family:仿宋;font-size:21px"><span style="font-family:仿宋">感谢以</span><span style="font-family:仿宋">2024届毕业生为主力军的防疫志愿者们，曾经在疫情期间为守护平安校园做出的努力与奉献，师生并肩作战、通力合作、守望相助，历历在目；同时祝福每位毕业生在人生的新起点扬帆起航</span></span><span style=";font-family:仿宋;font-size:21px">，</span><span style=";font-family:仿宋;font-size:21px">在今后的人生中坚定求知，不断探索。</span></p><p style=";text-align:center"><span style=";font-family:Calibri;font-size:16px">&nbsp;<img src="/uploads/20240529/e519a666b13173df122e8e1083c2229f.png" alt="图片2.png"/></span></p><p style="text-align: center;"><strong style="text-align: justify; text-indent: 250px;"><span style="font-family: 方正仿宋_GB2312;font-size: 15px">活动现场</span></strong></p><p style="text-indent:43px;text-autospace:ideograph-numeric;text-align:justify;text-justify:inter-ideograph"><span style=";font-family:仿宋;font-size:21px">活动进行中，与会领导为在场的毕业生代表发放纪念品，每位同学手写便利贴，诉说着对毕业生的祝福，满怀着自己未来的期望，张贴着对人文的情结。与此同时，转转盘赢奖品活动更是给毕业生自己的大学生涯画了一个圆满的句号。</span><span style="font-family: Calibri; text-align: center;">&nbsp;</span></p><p style="text-indent:43px;text-autospace:ideograph-numeric;text-align:justify;text-justify:inter-ideograph"><span style=";font-family:仿宋;font-size:21px">活动最后，各位领导和到场参与活动的所有学生一起拍照合影留念。此次活动增强了毕业生对母校的怀恋，增强了三栋楼宇全体同学间的交流，今后，将不断加强人文关怀，优化楼宇建设，促进优良学风和生活作风。</span></p><p style=";text-align:center"><strong><span style="font-family: 方正仿宋_GB2312;font-size: 15px">&nbsp;<img src="/uploads/20240529/d33123216f2b9fbc4c7f380a561807fb.png" alt="图片3.png"/></span></strong></p><p style="text-align: center;"><strong style="text-align: justify; text-indent: 236px;"><span style="font-family: 方正仿宋_GB2312;font-size: 15px">合影留恋</span></strong></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_经济与管理学院文旅教研室赴丹寨开展暑期调研实践活动</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000001280.shtml" class="text-decoration-none text-dark fw-bold">
                                                经济与管理学院文旅教研室赴丹寨开展暑期调研实践活动
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">经济与管理学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-07-22</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2124</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">为加强与行业、企业之间的沟通交流，了解企业对文旅人才的新需求，进一步打开校企协作的格局，经济与管理学院文旅教研室于7月19日至21日赴黔东南州丹寨县走访了丹寨万达小镇文旅相关企业、非遗村落，开展了行业调研实践活动。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">19日上午，教研室全体教师在教研室主任陈品冬教授的带领下来到丹寨万达小镇，万达小镇市场部经理罗凯就小镇的市场运营现状向各位老师进行了介绍。从罗经理的介绍中得知，万达小镇的运营以“非遗小镇”为主线，促进文旅融合，以文促旅，以旅带文，在旅游扶贫的公益先行模式下，以丰富的文旅业态吸引客流，持续提升小镇的自我造血功能。疫情后，万达小镇不断更新文旅活动，提升产品内涵、增强娱乐性，积极走出去与市场对接，持续挖掘深度研学市场，力求实现文旅破局新篇章。文旅教研室各位教师还与罗经理探讨了近期旅游市场复苏趋势，以及今年万达小镇旅游客流新特征，还就文旅小镇所需运营人才的能力培养、校企协作进行了探讨。<br style="box-sizing: border-box;"/></span></p><p style="line-height: 1.5em;"><img alt="8" height="600" src="/uploads/20231010/02a7f079166217a94cac0020e2c4c2b6.jpg" width="800" style="box-sizing: border-box; vertical-align: middle; width: 776px; height: auto; color: rgb(33, 37, 41); font-family: "/></p><p data-block-key="76kmu" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">19日下午，教研室全体教师来到丹寨万达锦华温泉度假酒店，与相关负责人进行洽谈，探讨校企合作意向及合作方式。酒店负责人介绍了万达酒店管理集团本科毕业生横纵向的培养模式，以及协同育人前置的“万达班”合作方式。教研室老师就旅游管理本科生职业素养的养成、酒店行业的职业前景等问题与酒店的相关负责人进行探讨。最后，陈品冬教授指出，校企合作是大学生创业就业教育的重要模式，应积极与相关企业建立、保持合作关系，本次校企合作洽谈是一项十分重要的工作。</span></p><p style="line-height: 1.5em;"><img alt="9" height="513" src="/uploads/20231010/2326db572c1e5ad66f53c3bed1ca1496.png" width="800" style="box-sizing: border-box; vertical-align: middle; width: 776px; height: auto; color: rgb(33, 37, 41); font-family: "/></p><p data-block-key="3898k" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p data-block-key="frbz6" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">20日上午，教研室全体教师与丹寨泓文国际康养研学中心的陈婷经理座谈，就研学旅行项目、康养旅游产品的市场现状展开研讨。研讨中，双方都高度评价专业实践对于应用型专业的重要性，只有将理论课堂与行业实践充分结合，才能保障育人成果。陈婷经理的创业就业经历是具有借鉴性的“教材”，相信能成为文旅专业学生的“榜样力量”。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">20日下午和21日上午，文旅教研室的老师们还先后调研了国春非遗银匠村、鸟笼编织艺术之乡卡拉村以及民族文化生态研学基地锦绣谷。</span></p><p style="line-height: 1.5em;"><img alt="10" height="600" src="/uploads/20231010/7e92f4207b09f096f60ce096c3640492.jpg" width="800" style="box-sizing: border-box; vertical-align: middle; width: 776px; height: auto; color: rgb(33, 37, 41); font-family: "/></p><p data-block-key="eaese" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">调研接近尾声，在回程中，教研室全体教师对本次调研活动进行复盘和总结。全体教师一致认为，疫情形式下，文旅行业遭受沉重打击，如何推动文旅融合，整合贵州特色资源，实现“双创”新格局成为行业研究新挑战。而后疫情时代，文旅行业加快复苏，“人才”成为最核心的竞争力，如何培养快速适应行业新发展、新要求的文旅人才成为新的命题。文旅教研室将持续关注行业变化，与行业内企业保持紧密交流与合作，开启校企协作新局面。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
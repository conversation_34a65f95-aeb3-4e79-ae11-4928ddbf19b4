<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院举行2024-2025第2学期首次升旗仪式</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/660492343828549.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院举行2024-2025第2学期首次升旗仪式
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-03-18</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="text-indent: 41px; margin-top: 5px; margin-bottom: 5px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体, SimSun; font-size: 20px;">3月10日、17日，体育与艺术学院本学期首次升旗仪式分别在大学城校区、花溪校区举行，学院团总支书记张鸿雁出席本次升旗仪式，学院970余名学生参加大学城校区升旗仪式，380余名学生参加花溪校区升旗仪式。</span></p><p style="text-align: center;"><img src="/resources/image/2025/03/31/660491593891909.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-align: center; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><strong><span style="font-family: 宋体, SimSun; font-size: 14px;">升旗仪式现场</span></strong></p><p style="text-indent: 41px; margin-top: 5px; margin-bottom: 5px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体, SimSun; font-size: 20px;">寒意料峭，晨光拂晓。清晨6点40，各班同学列队完毕，各项准备工作完毕。7点整，国旗护卫队成员迈着整齐有力的步伐护送五星红旗走到升旗台，伴随雄伟嘹亮的国歌，在全体师生的注目礼中鲜艳的五星红旗冉冉升起，现场氛围庄严肃穆。</span></p><p style="text-align: justify;"><img src="/resources/image/2025/03/31/660491682512965.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-align: center; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><strong><span style="font-family: 宋体, SimSun; font-size: 14px;">花溪校区国旗护卫队护送国旗&nbsp;</span></strong></p><p style="text-align: center;"><img src="/resources/image/2025/03/31/660491753959493.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-align: center; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><span style="font-size: 14px;"><strong><span style="font-family: 宋体, SimSun;">大学城校区升旗仪式</span></strong></span></p><p style="text-indent: 41px; margin-top: 5px; margin-bottom: 5px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体, SimSun; font-size: 20px;">升旗仪式现场，2022级休闲体育专业学生孔海艳丽、2024级环境设计专业学生陈亚楠分别做国旗下的讲话。</span></p><p style="text-indent: 41px; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><span style="font-family: 宋体, SimSun; font-size: 24px;"></span></p><p style="text-align: center;"><img src="/resources/image/2025/03/31/660491822043205.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="text-align: center; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><strong><span style="font-family: 宋体, SimSun; font-size: 14px;">孔海艳发表国旗下讲话</span></strong></p><p style="margin: 5px 0px; text-indent: 41px; text-align: justify; line-height: 2em;"><span style="font-family: 宋体, SimSun; font-size: 20px;">孔海艳以自身的学习经验和大家进行分享。她表示大三的自己和很多人一样存在着考研、考公、考编等问题的焦虑，但沉下心来后，在教室、寝室、食堂、图书馆四点一线奔波中逐渐清晰了自己的目标和认识。她鼓励低年级的学弟学妹珍惜当下，不负韶华，用心对待每一堂课，夯实好自身专业技能和综合素质，努力将眼前的 “未知” 走成一条光明的 “前程”。</span></p><p style="margin: 5px 0px; text-indent: 41px; text-align: justify; line-height: 2em;"><span style="font-family: 宋体, SimSun; font-size: 20px;">陈亚楠在讲话中以《与好书为伴》为主题，畅谈书籍的魅力和好处，倡导青年学生多读书、多好书，从书籍中感受文学魅力、汲取知识力量、涵养个人修养，不断提升自己的知识储备和人格修养。</span></p><p style="text-align: center;"><img src="/resources/image/2025/03/31/660491878285381.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="margin-right: 6px; text-align: center; margin-top: 5px; margin-bottom: 5px; line-height: 2em;"><strong><span style="line-height: 129%; font-family: 宋体, SimSun; font-size: 14px;">学院团总支书记张鸿雁讲话</span></strong></p><p style="margin: 5px 6px 5px 2px; text-indent: 43px; text-align: justify; line-height: 2em;"><span style="font-family: 宋体, SimSun; font-size: 20px;">学院团总支书记张鸿雁在升旗仪式上以电影《哪吒2》为切入点进行讲话，她希望大家学习向哪吒学习，在新学期不断淬炼思想，铸牢理想信念，在知行合一中“求真学问，练真本领”；在摆脱刻板束缚，突破成长边界的勇气和决心中破旧立新，突破自我；在勇于担当和团结集体的日常中实现共赢，让个人成长和集体荣誉在校园生活中同频共振。</span></p><p style="margin: 5px 6px 5px 2px; text-indent: 43px; text-align: justify; line-height: 2em;"><span style="font-family: 宋体, SimSun; font-size: 20px;">至此，学院本学期第一次升旗仪式完美落幕。此次升旗仪式不仅是一次简单的集体活动，也是一次全面的爱国主义教育和思想政治教育，通过升旗仪式让学院师生看到了书籍中蕴藏着的文化魅力、坚定了青春奋斗的决心、也从《哪吒2》的爆火中看到了中国的文化自信，激励着学院师生以哪吒的闯劲和拼劲在新的学期披荆斩棘、乘风破浪，取得更多的进步和突破。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
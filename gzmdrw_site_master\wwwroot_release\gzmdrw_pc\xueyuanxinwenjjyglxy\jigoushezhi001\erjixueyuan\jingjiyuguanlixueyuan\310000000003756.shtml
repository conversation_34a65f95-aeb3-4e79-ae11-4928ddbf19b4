<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_“牛司令”吴国邦为我院毕业班学生做就业创业讲座</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000003756.shtml" class="text-decoration-none text-dark fw-bold">
                                                “牛司令”吴国邦为我院毕业班学生做就业创业讲座
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-12-26</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2653</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-wrap: wrap; text-indent: 37px; text-align: justify; line-height: 33px;"><span style="font-family: 仿宋; font-size: 19px;">2023年12月19日和20日，贵州省人大代表、贵州青年五四奖章获得者、黎平县孺子牛养殖专业合作社理事长吴国邦，应邀为贵阳人文科技学院劳动与社会保障专业、行政管理专业2020级毕业班学生做线上讲座，分享大学求学、毕业创业就业和人大代表履职经历，给即将毕业的同学们带来课内外学习、就业创业和参与乡村振兴等各方面的启发。讲座由行政管理教研室教师石庆波老师主持，教研室主任邹晓抒等教研室全体教师在线收看。</span></p><p style="text-wrap: wrap;"><img src="/uploads/20231226/1e07333817b056740aa0bd87c619d8ac.png" alt="图片1.png"/></p><p style="text-wrap: wrap;"><span style="font-family: 仿宋; font-size: 19px;">&nbsp; &nbsp; 首先，吴国邦用谦逊的姿态向各位同学分享了自己的求学路，强调在学习中大量阅读的重要性，鼓励大家积极向学、努力致学。随后，他就自己在黎平县进行黄牛养殖的经验向大家讲述了自己创业促就业的经历，倡导大家努力拼搏、积极就业。接着，他就担任人大代表的提案建言作简要介绍：在大学生就业创业和乡村振兴工作中，尽责反映人民群众的呼声。最后，他寄语毕业班学生，希望大家能做有目标、有抱负、肯努力、能干事的人，多彩贵州的建设与发展需要青年同学们同心聚力、奋发拼搏、创新创业。</span></p><p style="text-wrap: wrap;"><span style="font-family: 仿宋; font-size: 19px;"><img src="/uploads/20231226/e3b3156f11c1d391de3c913d8fe9de60.png" alt="图片2.png"/></span></p><p style="text-wrap: wrap;"><br/></p><p style="text-wrap: wrap;"><span style="font-family: 仿宋; font-size: 19px;">&nbsp; &nbsp; 吴国邦是黎平县籍大学毕业生，返乡带动群众成立合作社养牛2000余头，解决季节性用工和兼职就业100余人。他的故事获《人民日报》刊登，省委书记点赞称他“金领白领不当，当牛司令”。讲座中，“牛司令”幽默的谈吐，丰富的人生阅历，让同学们受益颇深。班上同学认真聆听、思考，并积极提问。</span><span style="font-family: 仿宋; font-size: 19px;">&nbsp;</span></p><p style="text-wrap: wrap;"><span style="font-family: 仿宋; font-size: 19px;"><img src="/uploads/20231226/1836de70a2a9df37493fe6bb1c89c1d1.png" alt="图片3.png"/>&nbsp;&nbsp;&nbsp;</span></p><p style="text-wrap: wrap;"><span style="font-family: 仿宋; font-size: 19px;">&nbsp; &nbsp; 今年以来，按照我院教学数字化转型要求，行政管理教研室以《智慧政务》《大数据概论》等课程为突破口，积极采用“线下+线上”“校内+校外”“理论+实践”的方式开展教学试验，积极邀请党政部门领导、人大代表、智库专家、企业家、工程师等以各种方式参与本科生培养，深受师生欢迎。这样“理论+实践”的教学方式，是巩固理论知识</span><span style="font-family: 仿宋; font-size: 19px;">，加深理论认识的有效途径，有利于</span><span style="font-family: 仿宋; font-size: 19px;">学生</span><span style="font-family: 仿宋; font-size: 19px;">正确价值观的形成</span><span style="font-family: 仿宋; font-size: 19px;">和高素质人才的培养</span><span style="font-family: 仿宋; font-size: 19px;">。</span></p><p style="text-wrap: wrap;"><br/></p><p style="text-wrap: wrap; text-indent: 43px; text-align: right; line-height: 24px;"><br/></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
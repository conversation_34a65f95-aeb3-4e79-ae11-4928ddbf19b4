<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="01e3db2d-a2e7-4378-bc22-615b2581e998" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/fastapi-service/.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fastapi-service/DEPLOY.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fastapi-service/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fastapi-service/main.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/fastapi-service/requirements.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.gitignore" beforeDir="false" afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/chestnut-admin/src/main/resources/application-prod.yml" beforeDir="false" afterPath="$PROJECT_DIR$/chestnut-admin/src/main/resources/application-prod.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="jdkForImporter" value="ms-17" />
        <option name="workspaceImportForciblyTurnedOn" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="jreName" value="ms-17" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2y26dkKJhtJaANee663HYLGe5I5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.chestnut [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ChestnutApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;liurenjie&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/clon/gzmdrw_site_james/rabid-chestnut&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.javacompiler&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn install" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.ChestnutApplication">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="rabid-chestnut" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="rabid-chestnut" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ChestnutApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="chestnut-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chestnut.ChestnutApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ChestnutMonitorApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="chestnut-monitor" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.chestnut.ChestnutMonitorApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.19416.15" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.19416.15" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="01e3db2d-a2e7-4378-bc22-615b2581e998" name="Changes" comment="" />
      <created>1749020410106</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749020410106</updated>
      <workItem from="1749020411144" duration="3527000" />
      <workItem from="1749260102118" duration="2601000" />
      <workItem from="1749439259591" duration="4556000" />
      <workItem from="1749519119660" duration="1621000" />
      <workItem from="1749523029734" duration="6341000" />
      <workItem from="1749867537135" duration="2067000" />
      <workItem from="1749887577979" duration="6007000" />
      <workItem from="1749896346340" duration="631000" />
      <workItem from="1749955470776" duration="1807000" />
      <workItem from="1750054526222" duration="617000" />
      <workItem from="1750063176351" duration="603000" />
      <workItem from="1750666519714" duration="137000" />
      <workItem from="1750822425550" duration="2145000" />
      <workItem from="1750907538679" duration="1205000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>
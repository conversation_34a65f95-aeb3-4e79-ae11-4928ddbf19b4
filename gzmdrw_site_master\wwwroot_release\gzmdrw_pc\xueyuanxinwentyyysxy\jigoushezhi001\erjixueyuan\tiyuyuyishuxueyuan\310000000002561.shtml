<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院举行2020-2021学年第二学期第二次升旗仪式</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002561.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院举行2020-2021学年第二学期第二次升旗仪式
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2021-05-10</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1889</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background: rgb(255, 255, 255);text-indent: 37px;line-height: 37px"><span style="font-family: 仿宋;font-size: 19px"><span style="font-size: 18.6667px;text-indent: 37.3333px">为弘扬当代大学生爱国主义精神，践行社会主义核心价值观。</span>2021</span><span style="font-family: 仿宋;font-size: 19px">年</span><span style="font-family: 仿宋;font-size: 19px">5</span><span style="font-family: 仿宋;font-size: 19px">月</span><span style="font-family: 仿宋;font-size: 19px">10</span><span style="font-family: 仿宋;font-size: 19px">日</span><span style="font-family: 仿宋;font-size: 19px">7：00时整</span><span style="font-family: 仿宋;font-size: 19px">，</span><span style="font-family: 仿宋;font-size: 19px"><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">在我校中心广场举行</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">2020-2021学年第二学期第二次</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">升旗仪式</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">。参加此次升旗仪式的有学院团总支</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">、</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">学生会</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">、新闻中心以及部分学生代表共计</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">5</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">50名</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">学生，升旗仪式由我院团总支副书记</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">支胜杰</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">主持</span><span style="box-sizing: border-box;color: #333333;text-indent: 37.3333px;font-size: 19px">。<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background: rgb(255, 255, 255);text-indent: 37px;line-height: 37px;text-align: center"><img src="/uploads/20231021/5dcaaaa5f57e0c8cda5c7cc807c6ec7e.png" data-catchresult="img_catchSuccess"/></p><p style="margin-top: 0px;margin-right: 0;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 37px"><span style="font-family: 仿宋;font-size: 19px">本次升旗仪式主要希望我院学生同学们通过本次活动增强爱国意识和民族自豪感。不忘初心，铭记历史。让我院学生深深的感受曾经的革命烈士们抛头颅洒热血的悲壮，从而让大家明白这面鲜红的国旗它的来之不易。<br/></span></p><p style="margin-top: 0px;margin-right: 0;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 37px;text-align: center"><img src="/uploads/20231021/ee26b8900f72c7c9518e1f166ffaa9ea.png" data-catchresult="img_catchSuccess"/></p><p style="margin-top: 0px;margin-right: 0;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 37px"><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">升旗仪式结束后，20</span><span style="font-family:仿宋">18级体育教育专业2班学生徐远双作为学生代表作国旗下讲话。他说到五月是红色的，是神圣的，是一个值得人们铭记的时期，“五四”运动是五月里让人难忘的日子，那是革命先烈们用那火热的身躯染红的。历史总是回不去的，所以我们更加需要铭记那段历史，不忘初心。“少年强则国强”，不管前方有多么的艰难险阻，我们也要秉持着最火热的，最积极向上的心迎难而上，革命烈士们在那么艰难的情况下仍然为中国开辟了道路，为中国人民谋得了幸福。作为一名新时代的大学生，我们应该继承革命先辈们不怕吃苦，无所畏惧，勇往直前的精神和品质。</span></span></p><p style="margin-top: 0px;margin-right: 0;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 37px"><span style="font-family: 仿宋;font-size: 19px">通过</span><span style="font-family: 仿宋;font-size: 18.6667px;text-indent: 37.3333px">举行</span><span style="font-family: 仿宋;font-size: 19px;text-indent: 37px">本次升旗仪式，使我院参加升旗的学生代表意识到国旗代表一个国家，爱国是深藏在每一个人心中的理想。作为当代大学生，我院全体学生都应该认真学习专业知识，争取将来成为可以报效祖国的一员。铭记历史，开创未来，争取为祖国的繁荣富强贡献自己的力量。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
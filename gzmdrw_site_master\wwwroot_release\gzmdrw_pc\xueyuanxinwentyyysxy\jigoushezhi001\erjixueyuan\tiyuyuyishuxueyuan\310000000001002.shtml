<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_奋进新征程 谱写青春华章 庆祝党的二十大胜利召开美术 设计作品展 开幕</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000001002.shtml" class="text-decoration-none text-dark fw-bold">
                                                奋进新征程 谱写青春华章 庆祝党的二十大胜利召开美术 设计作品展 开幕
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-10-09</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2322</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">2022年12月13日下午，由体育与艺术学院党总支主办的 “奋进新征程，谱写青春华章——庆祝党的二十大胜利召开美术·设计作品展” 在大学城校区开幕。</p><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">此次展览开幕式由体育与艺术学院党总支书记王雪主持，校党委副书记马帅，体育与艺术学院院长雷帮齐，组织人事部副部长徐楠云，体育与艺术学院副院长车莹莹，体育与艺术学院党总支副书记邓程民，组织部老师唐林鹏、汤雷、潘琦以及体育与艺术学院党总支委员赵金亮、刘加坤等领导嘉宾和老师出席了本次开幕式。</p><p style="text-align: center;"><img src="/uploads/20231027/2d6317e97c964de68de006ba64748820.png" alt="KaiMuShiHeYing.max-1600x1600.format-webp"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">开幕式合影</div><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">本次展览旨在庆祝党的二十大胜利召开，践行举旗帜、聚民心、育新人、兴文化、展形象的使命任务，积极弘扬中华优秀文化和艺术精神，展现新时代青年大学生的精神风貌。</p><p style="text-align: center;"><img src="/uploads/20231027/96acf485236b8d98be01ba3938b71699.png" alt="LeiBangQiYuanZhangZhiCi.max-1600x1600.fo"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">雷帮齐院长致辞</div><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">雷帮齐院长在致辞中肯定了广大师生的辛勤付出，鼓励广大青年大学生在这个新的发展时期更好地肩负起历史的使命，争取取得更大的进步。</p><p style="text-align: center;"><img src="/uploads/20231027/81cd1086f78d18ed62e3b890b106c750.png" alt="XiaoDangWeiFuShuJiMaShuaiXuan.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">校党委副书记马帅宣布展览开幕</div><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p data-block-key="utdt" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">随后，校党委副书记马帅同志在全场热烈的掌声中宣布：奋进新征程，谱写青春华章——庆祝党的二十大胜利召开美术·设计作品展开幕！</p><p data-block-key="djqpm" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: ">本次美术·设计作品展，共收到报送作品156件，经过两轮严格评审，展出中国画、油画、版画、插画设计、海报设计等精品共计50余件。</p><p style="text-align: center;"><img src="/uploads/20231027/c2356dbe9fd828fc20a1242332bb8858.png" alt="ZhanLanXianChang.max-1600x1600.format-we"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">展览现场</div><p style="text-align: center;"><img src="/uploads/20231027/c3daa39483ad7b4536812ce4cb734890.png" alt="ZhanLanXianChang_csS8MWO.max-1600x1600.f"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">展览现场</div><p data-block-key="njqju" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); text-align: center;">部分入选作品</p><p style="text-align: center;"><img src="/uploads/20231027/1d2aafdc07533e28519b4578ef4a3701.png" alt="QianXiangFeiPu__ZhongGuoHua_2.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《黔乡飞瀑》 中国画 (2020级美术学专业国画班 毛宵)</div><p style="text-align: center;"><img src="/uploads/20231027/2a48a6d877a09d9c887d6e321c9f480a.png" alt="YunShanTu__ZhongGuoHua2020JiM.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《云山图》 中国画(2020级美术学专业国画班 李兴巧)</div><p style="text-align: center;"><img src="/uploads/20231027/6f9b059d31b5cedf339579f8bf6c2985.png" alt="YinJi__ZongHeCaiLiao_2019JiMe.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《印·迹》 综合材料 (2019级美术学专业综合班 陈慧)</div><p style="text-align: center;"><img src="/uploads/20231027/544483c3a616bd732bef8f5629d95c5e.png" alt="Ying5G__BingXiHua2021JiMeiShu.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《迎5G》 丙烯画(2021级美术学专业油画班 王翔)</div><p style="text-align: center;"><img src="/uploads/20231027/9a8fb639d63ce17534a9fbeb000a9afc.png" alt="Gang__BanHua_2020JiMeiShuXueZ.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《港》 版画 （2020级美术学专业综合班 陈悦）</div><p style="text-align: center;"><img src="/uploads/20231027/0f6d772687c289ea496fcb5ce2461a0d.png" alt="ShouWeiLouShanGuan__BanHua__2.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《守卫娄山关》 版画 （ 2019级美术学专业综合班 方毅龙）</div><p style="text-align: center;"><img src="http://www.gzmdrw.cn/static/addons/ueditor/ue/themes/default/images/spacer.gif" alt="《众志成城，贵阳加油》  海报设计 （2020级动画班 岳应红）" style="height: auto; text-align: center; text-wrap: wrap; box-sizing: border-box; vertical-align: middle; max-width: 100%; color: rgb(33, 37, 41);"/></p><p style="text-align: center;"><img src="/uploads/20231027/b1d2ec7b852d67abdf56efbb9e197105.png" alt="ZhongZhiChengChengGuiYangJiaY.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《众志成城，贵阳加油》 海报设计 （2020级动画班 岳应红）</div><p style="text-align: center;"><img src="/uploads/20231027/4e950b8dad0f20aa494a5bd5d88b567a.png" alt="XiYuZhongGuo__ChaHuaSheJi2020.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《戏·宇，中国》 插画设计（2020级美术学专业综合班 文江洪）</div><p style="text-align: center;"><img src="/uploads/20231027/5ad4107570cf2e9669a82310711a710e.png" alt="XiYingErShiDa__ChaHua2021JiMe.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《喜迎二十大》 插画（2021级美术学专业油画班 殷小双）</div><p style="text-align: center;"><img src="/uploads/20231027/0674bdb79d96fc88773d8d4f9968bd09.png" alt="XiYingErShiDa__HaiBaoSheJi202.max-1600x1"/></p><div class="img-center" style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center;">《喜迎二十大》 海报设计（2021级视觉传达班 王庭安）</div><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
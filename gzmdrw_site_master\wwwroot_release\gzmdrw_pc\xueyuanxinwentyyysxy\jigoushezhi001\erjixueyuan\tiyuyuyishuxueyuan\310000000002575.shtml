<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_【体艺校友风采】杨贵娇：向阳而生 逆风飞翔</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002575.shtml" class="text-decoration-none text-dark fw-bold">
                                                【体艺校友风采】杨贵娇：向阳而生 逆风飞翔
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2021-02-26</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1280</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px"><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">个人简介</span></strong><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">：</span></strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><span style="font-family:Microsoft YaHei UI">杨贵娇，</span>1989年1月15日生，女，汉族，政治面貌中共党员，现任职于贵州建设职业技术学院建筑设计学院副主管，职称讲</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">师</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px;text-align: center"><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><img src="http://172.16.2.190/userfiles/2021/04/image/1619175952391.jpg" class="loadingclass"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px"><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">工作经历：</span></strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">2011年7月毕业于贵州民族学院人文科技学院美术系美术学班；2011年7月-2012年9月任职于贵阳振华幼儿师范学校，任美术教师；2012年10月-2013年6月任职于贵州建筑职业技术学院，任专职教师；2013年7月-2016年5月任职于贵州建设职业技术学院建筑设计学院，任行政办公室负责人；2016年6月-2019年11月贵州建设职业技术学院建筑设计学院副科级干部，任职团总支书记、学生党支部书记；2019年12月担任贵州建设职业技术学院建筑设计学院副主管，负责顶岗实习工作。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px;text-align: center"><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><img src="http://172.16.2.190/userfiles/2021/04/image/1619175952391.jpg" width="600" height="450" alt="" style="border: 0px" class="loadingclass"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px"><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">获奖经历</span></strong><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">：</span></strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">2018年参加贵州省教育厅主办的贵州省职业院校技能大赛教学能力比赛荣获一等奖</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">；</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">2018年9月</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><span style="font-family:Microsoft YaHei UI">《苗彩吐祥瑞</span><span style="font-family:Microsoft YaHei UI">&nbsp;尤黛纳芬芳--苗族旅游产品插画设计》获得贵州省职业院校技能大赛教学能力比赛一等奖。<br/></span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px;text-align: center"><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><img src="http://172.16.2.190/userfiles/2021/04/image/1619175985146.jpg" class="loadingclass"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 40px;line-height: 33px"><span style="font-family: &#39;Microsoft YaHei UI&#39;;color: #FF8124;letter-spacing: 1px;font-size: 15px"><br/></span><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 16px">&nbsp; &nbsp; &nbsp; 校友寄语</span></strong><strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">：</span></strong><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">在人文的</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">四</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">年是让我快速成长的</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px">四</span><span style="font-family: &#39;Microsoft YaHei UI&#39;;letter-spacing: 2px;font-size: 15px"><span style="font-family:Microsoft YaHei UI">年，我从一个小女孩蜕变为一位职场者，谢谢母校的悉心栽培</span><span style="font-family:Microsoft YaHei UI">,使我们班的同学能够有今天的小小成就。愿母校能够发展蒸蒸日上，人文学子都能在祖国大地上写下绚烂一笔。</span></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_新生入学教育|启航新篇章，筑梦大学时光</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000005740.shtml" class="text-decoration-none text-dark fw-bold">
                                                新生入学教育|启航新篇章，筑梦大学时光
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-09-13</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>602</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="line-height: 2em; text-align: center;"><strong><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 18px;">启航新篇章，筑梦大学时光</span></strong></p><p style="line-height: 2em; text-align: center;"><strong><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 18px;">新生入学教育</span></strong></p><p style="text-align:center"><img src="/uploads/20240930/08b59c9271ae67406e98048f8dbc55f5.jpg" alt="微信图片_20240930195249.jpg"/></p><p style="text-align: justify; text-indent: 2em; line-height: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">为促进2024级新生更快融入新环境，尽快适应大学生活，进一步增进对党的认知。2024年9月2日、9月9日我院党总支书记徐楠云、副书记吕渊分别召开新生入学启蒙教育主题讲座。讲座全覆盖我院2024级新生。</span></p><p style="text-align: justify; text-indent: 2em; line-height: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">讲座分别围绕“入党启蒙教育”与“大学生职业生涯规划”两个部分。</span></p><p style="text-align: center;"><img src="/uploads/20240930/5ea0f5497b000dd806ed0954b25f1693.jpg" alt="微信图片_20240930195320.jpg"/></p><p style="line-height: 2em; text-indent: 2em; text-align: justify;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">讲座上，徐楠云书记与吕渊副书记分别深入浅出，不仅阐述了入党的重要意义，还详细解读了入党条件，入党基本程序、入党申请书的撰写六个环节，让同学们对入党的流程有了更清晰的认识和了解，激发同学们树立正确入党动机，鼓励同学们主动向党组织靠拢等。</span></p><p style="line-height: 2em; text-indent: 2em; text-align: justify;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">“立志以始，方能致远。”在谈到职业生涯规划时，徐楠云书记强调，个人职业规划需契合个性与志趣。她鼓励同学们根据自身特质逐步探索，虽道阻且长，但明确方向加之适时调整，必能渐行渐明。徐书记表示职业蓝图非一日之功，需持续审视与修订，在学习实践中明晰未来导向。</span></p><p style="text-align: center;"><img src="/uploads/20240930/9eb17aee9780f14693d53978ca178023.jpg" alt="微信图片_20240930195326.jpg"/></p><p style="line-height: 2em; text-indent: 2em; text-align: justify;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">吕渊副书记则着重讲解了职业规划的实践策略与价值，激励新生勇于自我提升，敢于追梦前行，为职业生涯的启航蓄力。<br/></span></p><p style="line-height: 2em; text-indent: 2em; text-align: justify;"><span style="color: #A5A5A5; font-family: 宋体, SimSun; font-size: 16px;">此次讲座，犹如启明星辰，指引新生笃定步伐，期许2024级所有新生同学以青春名义，誓行报国，以不懈奋斗描绘属于时代的绚烂青春篇章！</span></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
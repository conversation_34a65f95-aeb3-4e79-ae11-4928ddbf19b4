/*
 * Copyright 2022-2024 兮玥(<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.chestnut.advertisement;

/**
 * 广告素材类型
 */
public interface IAdvertisementType {

	/**
	 * Bean名称前缀
	 */
	public static final String BEAN_NAME_PREFIX = "AdvertisementType_";
	
	public String getId();
	
	public String getName();
}

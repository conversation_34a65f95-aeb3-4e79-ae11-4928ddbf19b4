<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_经济与管理学院工商管理教研室 开展教学能力提升系列教研活动</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000001126.shtml" class="text-decoration-none text-dark fw-bold">
                                                经济与管理学院工商管理教研室 开展教学能力提升系列教研活动
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">工商管理教研室</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2022-04-28</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2322</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">为提升经济与管理学院青年教师的教学水平与教学质量，推进校级一流课程、课程思政示范课程的培育建设。2022年4月27日，工商管理教研室全体教师于科研楼508会议室开展教学能力提升系列教研活动，本次会议由工商管理教研室主任罗雪菲老师主持，教研室各位教师围绕本学期所讲授课程的教学大纲与教案，分享理论教学、实践教学与课程思政教学的教学设计。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/2c7df6dff393803e0ad4daa2c10938e0.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图1 教研室主任主持召开教研活动</span></p><p data-block-key="yxsdz" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">罗雪菲老师分享了《市场营销学》的教学设计，介绍了该门课程以行业为导向的知识培养目标设置，以新技术赋能营销管理的能力培养目标设置，以文化自信、价值观念、职业素养为主线的素质培养目标设置，并以《市场营销学》第一章绪论为例，介绍了“学科回应”、“专业回应”与“课程思政融合”三大环节的教学设计内容。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">朱胡湘老师根据授课经验，对《品牌管理》课程设置提出了相关建议，重点偏向品牌管理相关的策划实践能力培养。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">担任《商务谈判》课程教学的滕建芳老师对课程思政教学设计进行了详细介绍。例如：“课程导入”、“谈判人员素质”、“谈判心理”、“商务谈判准备前工作”等课程知识点与“时代责任与时代使命”、“职业道德与职业功德”、“品格修养”、“良好的开始是成果的一半”等课程思政元素如何有效融合，并针对专业开放式作业的内容引导框架设置进行了经验分享与交流。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">阎梦老师针对《商务策划》课程，分享了具体的教学方法。每节课以5-6个案例为主线，按照框架讨论-模仿案例-小组作业呈现-现场发言-微助教作业提交等流程进行教学设计，主要培养学生的案例分析能力、团队合作能力、汇报表达能力，强调教师引导学生真实、客观想法的呈现。在案例的选择上，注重引导学生关注中国优秀企业的企业价值、营销理念等作为该门课程的课程思政切入点。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20231029/8e7be6f5f92edf29ea7cfd0f5923ee12.png" data-catchresult="img_catchSuccess"/></p><p style="box-sizing: border-box; color: rgb(33, 37, 41); text-align: center; line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">图2 各位教师热烈讨论现场</span></p><p data-block-key="yxsdz" style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); font-family: "><br/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">梁越老师就《采购学》中知识点与课程思政点的选择与融合提出了自己的独到见解。在“课程导入”、“政府采购”等部分内容，将“采购伦理”、“杜绝采购贪污”、“政府采购应该承担的社会责任”等课程思政元素融入教学过程中，培养学生辩证的思考能力。实践教学设计方面，邀请个体户、中小企业、大型连锁超市三类企业，按照企业家座谈讲解，学生提问、反馈，最后答疑、小组汇报的形式进行教学设计，旨在培养学生在不同的组织结构、不同的行业当中，切实理解采购管理环节的相关内容。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">颜红霞老师给各位老师分享了《经济法》课程的教学方式。在教学设计中，增加实践教学目标、思政教学目标与知识目标相配合。选用衣食住行中涉及经济行为的实际案例作为铺垫，以此引发学生的学习兴趣。课程思政部分强调价值观取向，例如，通过“经济法的宗旨与原则”的知识内容教学，引出培养学生梳理“公正观”的课程思政育人目标。</span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-indent: 2em; color: rgb(33, 37, 41); line-height: 1.5em;"><span style="font-size: 20px; font-family: 宋体, SimSun;">本次教研室活动中，老师们依次分享了课程的教学设计经验，共同探讨了专业课程建设规划，经过充分交流，提出“以创业教育型”大学定位转型为契机，积极推进市场营销专业、电子商务专业课程群建设的教学改革设想。拟组建1-2支教学团队，以知识、能力、素养目标培养为主线，以集体备课为方式，构建分阶段、分培养板块、分课程的专业课程群模式。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学部开展2015级新生安全警示教育</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002487.shtml" class="text-decoration-none text-dark fw-bold">
                                                学部开展2015级新生安全警示教育
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">教育与艺术学部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2015-10-26</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2004</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><div class="row" align="center" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><h1 style="margin: 0px; padding: 0px; font-size: 20.8px;"><br/></h1></div><div class="row" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><div align="center" style="margin: 0px; padding: 0px;"><strong><span style="font-size: 16pt;">学部开展2015级新生安全警示教育</span></strong></div><div style="margin: 0px; padding: 0px; text-indent: 28pt;"><span style="font-size: 14pt;">为进一步使同学们清楚地了解社会各类复杂的诈骗手段，增强新生自我安全意识，我学部于2015年10月23日下午，邀请花溪区银晖派出所民警李小鹏为我学部新生开展安全警示教育主题讲座。讲座在2号楼2楼展厅举行，由教育与艺术学部党总支学生工作临时负责人王雪老师主持，学院保卫处潘淏老师、学部辅导员金铭老师、专职教师赵金亮老师，以及学部2015级全体新生参加。<br/></span></div><div style="margin: 0px; padding: 0px; text-align: center; text-indent: 28pt;"><span style="font-size: 14pt;"><img src="/uploads/20231021/bdd82913ce970a8355788e1633170d2d.png" alt="image.png"/></span></div><div align="left" style="margin: 0px; padding: 0px; line-height: 19.5px; text-indent: 28pt;"><span style="line-height: 28px; font-size: 14pt;">李警官以幽默风趣的语言打开话题，让气氛变得轻松融洽。他针</span><span style="line-height: 28px; font-size: 14pt;">对各类诈骗案件为同学们做了详细的分析，如校园诈骗案通常以冒充学校老师、谎称学生出现事故、网络兼职等理由，对在校生实施诈骗。李警官还为同学们讲解了防骗常识与技巧，并对同学们提出了以下几</span><span style="line-height: 28px; font-size: 14pt;">点建议：一、网络、电话的虚拟性强，不要轻易泄露个人信息；二、给家人留下老师或室友的手机号作为紧急联系电话；三、莫贪小便宜，天下没有免费午餐；四、遇事多考虑，不要冲动，以至于酿成无法估计的后果。<br/></span></div><div style="margin: 0px; padding: 0px; text-align: center; line-height: 19.5px; text-indent: 28pt;"><span style="line-height: 28px; font-size: 14pt;"><img src="/uploads/20231021/27500994c70e0c53bad305759cf943c5.png" alt="image.png"/></span></div><div align="left" style="margin: 0px; padding: 0px; line-height: 19.5px; text-indent: 12pt;">&nbsp;&nbsp;<span style="line-height: 28px; font-size: 14pt;">最后，李警官还向同学们讲述了很多他亲自接到过的案件，让同学们清楚地知道诈骗所带来的伤害和损失。李警官在讲座中多次强调并警示新生们不要前往人员密集、鱼龙混杂的场所，不租住有安全隐患的出租屋，不酗酒晚归，遵守校规校纪；此外，他还要求同学们学</span></div><div align="left" style="margin: 0px; padding: 0px; line-height: 19.5px;"><span style="line-height: 28px; font-size: 14pt;">会克制自身情绪，切勿打架斗殴。当自己或身边的同学遇到任何有关安全的问题，可直接拨打银晖派出所电话（836121100）进行咨询求助。</span></div></div><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
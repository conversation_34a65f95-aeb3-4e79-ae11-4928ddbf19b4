<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院团总支开展 “光盘有爱，青春先行 ”主题团日活动</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002590.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院团总支开展 “光盘有爱，青春先行 ”主题团日活动
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2020-10-16</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1966</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 35px"><span style="font-family: 仿宋;font-size: 19px">为深入学习贯彻习近平总书记关于厉行节约、制止餐饮浪费的重要指示</span><span style="font-family: 仿宋;font-size: 19px">精神。</span><span style="font-family: 仿宋;font-size: 19px">2020年</span><span style="font-family: 仿宋;font-size: 19px">10月</span><span style="font-family: 仿宋;font-size: 19px">16</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">日</span>1</span><span style="font-family: 仿宋;font-size: 19px">3</span><span style="font-family: 仿宋;font-size: 19px">:</span><span style="font-family: 仿宋;font-size: 19px">0</span><span style="font-family: 仿宋;font-size: 19px">0，</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">体育与艺术学</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px"><span style="font-family:仿宋_GB2312">院团总支在科研楼</span>301会议室开展“</span><span style="font-family: 仿宋;font-size: 19px">光盘有爱，青春先行</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">”主题团日活动，我院团总支、学生会、新闻中心学生</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">干部和部分学生</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">代表参加此次</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">活动</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">，</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">活动</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">由团总支</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">秘书长蔡奎</span><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px">主持。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 35px;text-align: center"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px"><img src="/uploads/20231021/31a78066376292a5fa177b8a4137fa29.png" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 35px"><span style="font-family: 仿宋;font-size: 19px">活动中，</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">蔡奎全面地介绍了</span>“</span><span style="font-family: 仿宋;font-size: 19px">光盘</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">有爱</span>·青春先行</span><span style="font-family: 仿宋;font-size: 19px">”</span><span style="font-family: 仿宋;font-size: 19px">活</span><span style="font-family: 仿宋;font-size: 19px">动的</span><span style="font-family: 仿宋;font-size: 19px">宣传</span><span style="font-family: 仿宋;font-size: 19px">意义</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">。他说到：</span>“</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">开展</span>“光盘</span><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">有爱</span>·青春先行”主题团日活动，旨在通过开展光盘行动，弘扬浪费为耻、节约为荣的传统美德，使珍惜粮食成为同学们的自觉行为，</span><span style="font-family: 仿宋;font-size: 19px">自觉形成勤俭节约的良好社会风尚</span><span style="font-family: 仿宋;font-size: 19px">”。</span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 35px"><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">活动中，大家纷纷表示，青年大学生不仅是</span>“光盘有爱，青春先行”的宣传者，更是活动的参与者与践行者。</span><span style="font-family: 仿宋;font-size: 19px">引导广大青年传承勤俭节约、艰苦奋斗的优良家风是我们义不容辞的责任。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
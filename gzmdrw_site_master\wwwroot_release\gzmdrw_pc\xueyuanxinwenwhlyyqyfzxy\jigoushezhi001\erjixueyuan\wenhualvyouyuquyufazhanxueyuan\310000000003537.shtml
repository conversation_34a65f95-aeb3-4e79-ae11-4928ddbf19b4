<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_“爱我中华，共筑中国梦”|我院团总支学生会举行升旗仪式</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000003537.shtml" class="text-decoration-none text-dark fw-bold">
                                                “爱我中华，共筑中国梦”|我院团总支学生会举行升旗仪式
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-11-27</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2994</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; visibility: visible; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em; visibility: visible;">青年有信仰，民族就有希望；青年有担当，民族就有未来。为了<span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; visibility: visible; background-color: initial;">弘扬当代大学生爱国主义情怀,丰富当代大学生爱国主义精神,践行社会主义核心价值观,增强同学们的爱国意识、爱国情怀。文化旅游与区域发展学院于11月20日早上7：00在花溪校区教学楼前组织开展主题为“爱我中华，共筑中国梦”的升旗仪式。</span></span></section><p><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;">本次升旗仪式由院团总支组织部的杨佳琳同学主持，彭丹老师出席本次活动，我院2023级全体学生参加。</span></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><p id="_img_parent_tmp" style="text-align:center"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><img src="/uploads/20231129/bc50d1e5d3fb1d77e36e42a866406a30.jpg" alt="1.jpg"/></span></p></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;">全场肃静，随着主持人“出旗”口令的发出，国旗护卫队身抗国旗，扬雄风、挺傲骨，步履铿锵地护着五星红旗正步走向旗杆。随着激昂的《义勇军进行曲》响起，全体师生面向国旗行注目礼，冉冉升</span><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;">起的五星红旗，在冬日的暖阳中迎风飘扬。</span></span></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><p id="_img_parent_tmp" style="text-align:center"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><img src="/uploads/20231129/841b5451870cdda440062f839ab6ec4a.jpg" alt="2.jpg"/></span></span></p></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><p id="_img_parent_tmp" style="text-align:center"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><img src="/uploads/20231129/4a881d71c9ef217af2c13813720c51b3.jpg" alt="3.jpg"/></span></span></p></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;">随后，2023级农艺教育1班的李正彬同学作为学生代表发言，他表示：“热爱自己的祖国是立身之本，我们生在红旗下，长在春风里。我们今天的幸福生活，是革命先辈们抛头颅、洒热血艰苦奋斗来的。我们应该做一个懂得感恩的人，在自己的能力范围内回馈国家，为共筑中国梦奉献自己的青春力量。”</span></span></span></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><p id="_img_parent_tmp" style="text-align:center"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><img src="/uploads/20231129/becdb1971f91e74e2ab9a325d8bf57e2.jpg" alt="5.jpg"/></span></span></span></p></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 32px; text-align: center; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;">升旗仪式结束后，彭丹老师作了题为《坚定理想信念，成就卓越自我》的讲话，“人间四季，惟奋斗者强，惟奋斗者进。学生的主要任务是学习，勤奋学习是你们最美的姿态，你们要振拼搏之翼，品学习之美，书青春之卷，”她鼓励同学们在大学四年里专注学习，立鸿鹄之志，不因外界纷扰而改变自己的理想信念，坚信努力奋斗的汗水，终将浇灌出成功的花。</span></span></span></span></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 32px; text-align: center; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><img src="/uploads/20231129/fb57c0287070be888334b44cbe02ac8e.jpg" alt="6.jpg"/></span></span></span></span></section><section style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; color: rgb(233, 61, 38); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 13px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 2em; text-wrap: wrap; widows: 1; text-align: justify; text-indent: 2em; background-color: rgb(254, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; font-family: 宋体; font-size: 16px; color: #888888; line-height: 2em;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 34px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; color: #888888; font-family: 宋体; background-color: #FFFFFF; box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 0.544px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 32px; text-align: center; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 32px; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;">本次“爱我中华，共筑中国梦”升旗仪式活动的开展既有利于弘扬伟大的爱国主义精神，加强我院学生的爱国之情，坚定理想信念，又有利于我院营造积极向上的良好风尚，引导学生积极树立自己的人生目标，勇于承担身为新时代新青年的责任。</span></span></span></span></span></section></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_喜报|学院师生在第十八届中国好创意暨全国数字艺术设计大赛中喜获佳绩</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000005608.shtml" class="text-decoration-none text-dark fw-bold">
                                                喜报|学院师生在第十八届中国好创意暨全国数字艺术设计大赛中喜获佳绩
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-08-29</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>892</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 37px;line-height: 150%"><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px"><span style="font-family:宋体">近日，</span><span style="font-family:宋体">2024年第十八届中国好创意暨全国数字艺术设计大赛贵州分赛区竞赛结果揭晓，经赛事组委会评审，学</span></span><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px"><span style="font-family:宋体">院<span style="font-family: 宋体; font-size: 19px; text-indent: 37px; text-wrap: wrap;">师生</span></span><span style="font-family:宋体">8件作品在此竞赛中斩获佳绩，其中贵州分赛区二等奖1项、三等奖7项。 </span></span></p><p style="text-indent: 37px;line-height: 150%"><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px"><span style="font-family:宋体">中国好创意暨全国数字艺术设计大赛（</span><span style="font-family:宋体">China Creative Challenges Contest简称3C大赛或“中国创意挑战大赛”），是入选教育部中国高等教育学会发布的《全国普通高校大学生竞赛分析报告》赛项，大赛旨在落实国家数字创意产业远景规划，转化高等院校原创知识产权，深度挖掘、选拔和推广中国创意界的精英人才和优秀作品。</span></span></p><p style="text-indent: 37px;line-height: 150%"><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px"><span style="font-family:宋体">在</span><span style="font-family:宋体">2024年第十八届中国好创意暨全国数字艺术设计大赛贵州分赛区竞赛中，学院环境设计、动画、视觉传达设计专业教师积极组织和指导学生进行创作参赛。最终，经过校内选拔，学院共报送50件作品参加本次大赛。</span></span></p><p style="text-align:center;line-height:150%"><img src="/uploads/20240912/e1225afe36d7e5fe811a94e0215f2047.jpg" alt="喜报获奖证书汇总_00.jpg"/><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px">&nbsp;</span></p><p style="text-align:center;line-height:150%"><img src="/uploads/20240912/c80a49dda6b4a847cb1f5a776078ddec.jpg" alt="喜报获奖证书汇总_01.jpg"/><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px">&nbsp;</span></p><p style="text-align:center;line-height:150%"><img src="/uploads/20240912/366a220c869c91a7dbbe20fa6025f569.jpg" alt="喜报获奖证书汇总_02.jpg"/><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px">&nbsp;</span></p><p style="text-align:center;line-height:150%"><img src="/uploads/20240912/4a99ea32ee4c0579b61290f42b966a29.jpg" alt="喜报获奖证书汇总_03.jpg"/></p><p style="line-height: 150%; text-align: center;"><img src="/uploads/20240912/c5330afacd60dd45418a4e765dcfad1b.jpg" alt="喜报获奖证书汇总_04.jpg"/></p><p style="line-height: 150%; text-align: center;"><img src="/uploads/20240912/61697e6abfc205c2fb1017b32f1d811c.jpg" alt="喜报获奖证书汇总_05.jpg"/></p><p style="line-height: 150%; text-align: center;"><img src="/uploads/20240912/e5e94ba903fe2a988bba96bff4837c81.jpg" alt="喜报获奖证书汇总_06.jpg"/></p><p style="line-height: 150%; text-align: center;">&nbsp;<img src="/uploads/20240912/e5e94ba903fe2a988bba96bff4837c81.jpg" alt="喜报获奖证书汇总_06.jpg"/><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 19px">&nbsp;</span></p><p style="text-align:center;line-height:150%"><strong><span style="font-family: 宋体;line-height: 150%;letter-spacing: 0;font-size: 16px">获奖证书</span></strong></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_喜报|学院教师在全国数字创意教学技能大赛国赛中斩获佳绩</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000005821.shtml" class="text-decoration-none text-dark fw-bold">
                                                喜报|学院教师在全国数字创意教学技能大赛国赛中斩获佳绩
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-10-14</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>545</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent:38px;line-height:150%"><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">近日，第</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">七</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">届全国数字创意教学技能大赛国赛获奖名单公示，</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">学</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">院艺术设计教研室冯凤娇老师主讲的课程《环境设计数字化表现》</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">在比赛中</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">斩获国家级三等奖。</span></span></p><p style="line-height: 150%; text-align: center;"><img src="/uploads/20241017/52e3bf0beccf60f7d241e392f4a4f260.png" alt="国赛高校公示获奖名单截图1.png"/><span style=";font-family:宋体;font-size:14px">&nbsp;</span></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:0;text-align:center"><span style="font-family: 宋体;color: #333333;letter-spacing: 0;font-size: 16px;background: #FFFFFF"><span style="font-family:宋体">全国数字创意教学技能大赛国赛国赛高校公示获奖名单截图</span></span></p><p style="text-indent: 38px;line-height: 150%"><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">全国数字创意教学技能大赛（</span><span style="font-family:宋体">National Competition for Digital Creative Teaching Skills，缩写：NCDCTS）由全国高等院校计算机基础教育研究会、全国数字创意教学技能大赛组织委员会主办，全国高等院校计算机基础教育研究会数字创意专业委员会承办。大赛由中国高等教育学会认定，成为高教学会发布的全国普通高校教师教学竞赛榜单赛事，纳入教育部中国高等教育学会《全国普通高校教师教学竞赛分析报告》，是高等高等教育数字化改革，推动教师专业发展和教学能力提升，创新人才培养的重要竞赛，在高校教师技能大赛领域具有广泛影响力。</span></span></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:29px;padding:0 0 0 0 ;text-align:center;line-height:150%"><img src="/uploads/20241017/e3fa06b218a67fffeafef7413dfe104f.png" alt="高校教师教学竞赛分析报告.png"/><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 15px">&nbsp;</span></p><p style="margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;text-indent:32px;padding:0 0 0 0 ;text-align:center;line-height:150%"><span style="font-family: 宋体;color: #333333;letter-spacing: 0;font-size: 16px">高校教师教学竞赛分析报告</span></p><p style="text-indent: 38px;line-height: 150%"><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">学</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">院</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">长期</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">坚持以赛促学、以赛促教、以赛促研、以赛促改的总体思路，</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">鼓励青年教师积极参与各类教学技能竞赛，并邀请相关领域专家进行指导，力求通过竞赛不断地学习和提升</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">教师教学实践创新应用能力，推动</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">学院</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">课程教学改革、教师专业发展，</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">不断</span></span><span style="font-family: 宋体;line-height: 150%;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">适应新时代高等教育数字化改革和创新驱动发展。</span></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
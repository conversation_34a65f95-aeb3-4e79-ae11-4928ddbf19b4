<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院团总支组织召开“五四”表彰会议</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002345.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院团总支组织召开“五四”表彰会议
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2019-05-06</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1793</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin: 0 8px;text-indent: 37px;padding: 0"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">辛勤耕耘，喜见硕果累累；努力刻苦，又获佳绩连连。</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF">2019</span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">年</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">5</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">月</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">7</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">日</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">13</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">：</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">00</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">，体育艺术学院组织团总支学生会于科研楼</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">301</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">会议室召开</span><span style="font-family:宋体">“五四”表彰会议，会议由我院团总支书记支胜杰主持。</span></span></p><p style="margin-top:0;margin-right:8px;margin-bottom:0;margin-left:8px;text-indent:37px;padding:0 0 0 0 ;text-align:center"><img src="/uploads/20231021/1ca9a6476c3823d08713cc0560e806ec.jpg" data-catchresult="img_catchSuccess"/><span style=";font-family:Calibri;font-size:14px">&nbsp;</span></p><p style="margin: 0 8px;text-indent: 37px;padding: 0"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">会上，支胜杰宣布，我院团总支在</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">2018</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">年度的工作中表现优秀，荣获贵州民族大学人文科技学院</span><span style="font-family:宋体">“五四红旗团总支”荣誉称号。支胜杰强调，获奖体现出我院团总支学生会对工作的认真负责，是彰显我院突破自我提升的表现，希望我院团总支学生会在今后努力的工作中，不断打造属于体育与艺术学院的独特风采。</span></span></p><p style="margin-top:0;margin-right:8px;margin-bottom:0;margin-left:8px;text-indent:37px;padding:0 0 0 0 ;text-align:center"><img src="/uploads/20231021/c6edf31e69b386acade0d5d7ab3a2bcd.jpg" data-catchresult="img_catchSuccess"/><span style=";font-family:Calibri;font-size:14px">&nbsp;</span></p><p style="margin: 0 8px;text-indent: 37px;padding: 0"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">会上，支胜杰对团总支学生会在</span></span><span style="font-family: 微软雅黑;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:微软雅黑">2018</span></span><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">年度的工作中表现优秀的同学表示肯定与感谢，并给团总支学生会各个部门新换届的学生干部颁发聘书。支胜杰说到，颁发聘书是对每位同学尽职尽责做好每一件事的鼓励，可以促进我院团总支学生会朝着规范、健康、有序的方向发展。</span></span></p><p style="margin-top:0;margin-right:8px;margin-bottom:0;margin-left:8px;text-indent:37px;padding:0 0 0 0 ;text-align:center"><img src="/uploads/20231021/f3b287e6816987fb8ca89f5af91a3117.jpg" data-catchresult="img_catchSuccess"/><span style=";font-family:Calibri;font-size:14px">&nbsp;</span></p><p style="margin: 0 8px;text-indent: 37px;padding: 0"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:宋体">会上，支胜杰说到，我院团总支学生会在日常活动过程中不畏时间紧迫，奋发进取，积极准备各项工作，勤奋刻苦，展现出了我院团总支学生会良好的精神风貌和较高的知识水平。希望团总支学生会各部门互相之间能够进一步加强合作，形成优势互补，时刻严格要求自己。希望各部门再接再厉，相互帮助，共同进步。</span></span></p><p style="margin: 8px;border-bottom: 1px dashed rgb(128, 155, 185);padding: 0 0 12px"><span style=";font-family:Calibri;font-size:14px">&nbsp;</span></p><p><span style=";font-family:Calibri;font-size:14px">&nbsp;</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
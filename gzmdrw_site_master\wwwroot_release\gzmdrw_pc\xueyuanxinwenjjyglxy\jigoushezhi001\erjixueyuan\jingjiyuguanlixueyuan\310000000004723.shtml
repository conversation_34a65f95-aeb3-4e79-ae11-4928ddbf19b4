<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_经济与管理学院“贵阳市大数据发展实践探索” 学术讲座圆满结束</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000004723.shtml" class="text-decoration-none text-dark fw-bold">
                                                经济与管理学院“贵阳市大数据发展实践探索” 学术讲座圆满结束
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-13</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1298</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin: 3px 0px; text-indent: 25px; line-height: 1.5em;"><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;">面对全球性大数据浪潮的背景下，为了帮助学生深入了解行业的发展趋势，抓住时代发展的机遇，通过了解贵阳大数据产业的发展实践，旨在提升其综合素质和竞争力，并为未来的职业道路发展打下坚实基础。5月9日，我院行政管理教研室邀请到贵阳市大数据局机关党委委员、贵州省综合评标专家潘琼刚老师为行政管理、电子商务等专业学生带来了一场主题为“贵阳市大数据发展实践探索”的精彩讲座。</span></p><p style="margin: 3px 0px; text-indent: 25px; line-height: 1.5em;"><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;">讲座中，潘琼刚老师从当前贵州发展的困境、全球掀起大数据浪潮、中国国际大数据产业博览会、&nbsp;贵阳大数据发展新篇章四个方面进行展开。首先，潘老师从贵州发展的困境引入，向同学们系统展示了独属于贵州大数据的发展蓝图，并提出面对“美丽贵州”与“贫困贵州”之间的矛盾问题，采用何种方法能平衡两者值得我们去思考。其次，其对美国、欧盟、中国等国家和地区发展大数据的情况进行了介绍，并引出“贵州、贵阳：乘势而上，赢得先机——关键的2014、2015年”这一主题，分析了贵州、贵阳发展大数据的优势和发展历程。再次，潘老师作为中国国际大数据产业博览会的主要工作人员，其结合自己的工作经历对中国国际大数据产业博览会进行了全面介绍，通过多个案例进行了深入分析。最后，潘老师从数字经济发展“一二三四”工作思路等方面阐述了贵阳大数据发展新篇章。同时指出，作为新时代大学生，如何能在大数据发展实践中探索家乡的发展之路是我们应该不断关注的主题，这对我院的学生来说具有非常重要的实践价值。</span></p><p style="margin: 3px 0px; text-indent: 0px; text-align: center; line-height: 1.5em;"><span style="letter-spacing: 0px; font-size: 20px;"><img src="/uploads/20240513/9a52c5d9ef94614f939af724958f589a.png" alt="图片1.png"/>&nbsp;</span><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;">图1 潘琼刚老师讲座现场</span></p><p style="margin: 3px 0px; text-indent: 25px; line-height: 1.5em;"><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;">互动环节，同学们结合自身专业与潘老师进行了现场交流。2021级行政管理专业学生提出面对当前的不太乐观的就业环境，行管专业学生应该如何应对？潘老师回答到当前就业形势不太好，希望同学们能立足于当下，把握好任何机会，在校期间认真学习好专业知识，打下坚实的理论基础，建议先就业再择业。</span></p><p style="margin: 3px 0px; text-indent: 0px; text-align: center; line-height: 1.5em;"><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;"><img src="/uploads/20240513/f7ce3dfe93c8c49c28a545c1447d9d08.png" alt="图片2.png"/>&nbsp;</span><span style="font-family: 仿宋; letter-spacing: 0px; font-size: 20px;">图2 学生互动提问</span></p><p style="margin: 3px 0px; text-indent: 25px; line-height: 1.5em;"><span style="font-size: 20px;"><span style="letter-spacing: 0px; font-family: 仿宋; font-size: 20px;">通过此次讲座，进一步增强了同学们对大数据的整体了解，也提高了同学们对贵州大数据发展的信心，为贵州大数据的纵深发展积累了青年力量。讲座结束后，我院行政管理教研室主任邹晓抒老师向潘琼刚颁发了“校外导师”聘书，正式聘请潘琼刚作为我院的“校外导师”。希望通过“校外导师”这一合作形式，增强我院的实践教学能力，进一步提升我院人才培养质量，助力我院学生高质量就业。</span><span style="font-family: 宋体; letter-spacing: 0px; font-size: 20px;">&nbsp;</span></span></p><p style="margin: 3px 0px; text-indent: 0px; text-align: center; line-height: 1.5em;"><span style="letter-spacing: 0px; font-size: 20px;">&nbsp;<img src="/uploads/20240513/ddf919ef3c7f88dfbc8fdbd06029a26d.png" alt="图片3.png"/></span></p><p style="margin: 3px 0px; text-indent: 0px; text-align: center; line-height: 1.5em;"><span style="font-size: 20px;"><span style="font-family: 宋体; letter-spacing: 0px; font-size: 20px;">图</span><span style="font-family: Calibri; letter-spacing: 0px; font-size: 20px;">3&nbsp;</span><span style="letter-spacing: 0px; font-family: 宋体; font-size: 20px;">颁发“校外导师”聘书</span></span></p><p style="margin-top:3px;margin-right:0;margin-bottom:3px;margin-left:0;text-indent:0;text-align:center"><span style="font-family: 宋体;letter-spacing: 0;font-size: 11px">&nbsp;</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_体育与艺术学院举行2020-2021学年第二学期首次升旗仪式</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002570.shtml" class="text-decoration-none text-dark fw-bold">
                                                体育与艺术学院举行2020-2021学年第二学期首次升旗仪式
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">体育与艺术学院</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2021-03-15</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1835</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋;font-size: 19px">2021年03月15日7:00，体育与艺术学院</span><span style="font-family: 仿宋;font-size: 19px">团总支</span><span style="font-family: 仿宋;font-size: 19px">在我校中心广场举行</span><span style="font-family: 仿宋;font-size: 19px">2020-2021学年第二学期首次</span><span style="font-family: 仿宋;font-size: 19px">升旗仪式</span><span style="font-family: 仿宋;font-size: 19px">。参加此次升旗仪式的有学院团总支</span><span style="font-family: 仿宋;font-size: 19px">、</span><span style="font-family: 仿宋;font-size: 19px">学生会</span><span style="font-family: 仿宋;font-size: 19px">、新闻中心以及部分学生代表共计</span><span style="font-family: 仿宋;font-size: 19px">5</span><span style="font-family: 仿宋;font-size: 19px">50名</span><span style="font-family: 仿宋;font-size: 19px">学生，升旗仪式由我院团总支副书记</span><span style="font-family: 仿宋;font-size: 19px">支胜杰</span><span style="font-family: 仿宋;font-size: 19px">主持</span><span style="font-family: 仿宋;font-size: 19px">。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px;text-align: center"><span style="font-family: 仿宋;font-size: 19px"><img src="/uploads/20231021/11511e0c0b36a6b34411e83052de998c.png" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">升旗仪式于</span><span style="font-family:仿宋">7:00准时开始，</span></span><span style="font-family: 仿宋;font-size: 19px">学校</span><span style="font-family: 仿宋;font-size: 19px">国旗队的</span><span style="font-family: 仿宋;font-size: 19px">升</span><span style="font-family: 仿宋;font-size: 19px">旗手们迎着朝阳，踏着矫健的步伐，以饱满的热情，庄严地护卫五星红旗入场。随后，雄壮有力的《义勇军进行曲》响彻了整个校园，</span><span style="font-family: 仿宋;font-size: 19px">体育与艺术学院团总支参加升旗的全体师生</span><span style="font-family: 仿宋;font-size: 19px">集体行注目礼，全场弥漫着一种庄严肃穆的气息。冉冉升起的五星红旗激发了同学们不断向上的斗志，点燃了同学们强烈的爱国热情。<br/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px;text-align: center"><span style="font-family: 仿宋;font-size: 19px"><img src="/uploads/20231021/c6c5e279979a820e6bbd46ac7a35726d.png" data-catchresult="img_catchSuccess"/></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋;font-size: 19px"><span style="font-family:仿宋">升旗礼毕，在庄严的国旗下，我院学生代表</span><span style="font-family:仿宋">2018级体育教育2班雷志鑫作了以“耕耘大学生活，收获成长苦乐”为题的国旗下讲话。他用“时光飞逝，岁月如梭。”让我们不禁的感慨着大学生活似乎一晃就已经过去了一半的时间；他用“长风破浪会有时，直挂云帆济沧海。”提醒着我们要扬起奋斗的风帆，去勾勒理想的蓝图，既然选择了远方，便只顾风雨兼程。</span></span></p><p style="margin-top: 0px;margin-bottom: 0px;padding: 0px;font-family: &#39;Microsoft YaHei&#39;;font-size: 13px;text-wrap: wrap;background-color: rgb(255, 255, 255);text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋;font-size: 19px">本次开学以来，我院第一次升旗仪式圆满结束。初春的花香，盎然的绿意，到处都是生机勃勃，</span><span style="font-family: 仿宋;font-size: 19px">希望我院全体学生</span><span style="font-family: 仿宋;font-size: 19px">将不惧艰险，享受大学里每一次困难的磨练，抒写属于</span><span style="font-family: 仿宋;font-size: 19px">自己</span><span style="font-family: 仿宋;font-size: 19px">人生最美好的篇章</span><span style="font-family: 仿宋;font-size: 19px">。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_教育与艺术学部团总支学生会学习习近平‘5.9’讲话</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002602.shtml" class="text-decoration-none text-dark fw-bold">
                                                教育与艺术学部团总支学生会学习习近平‘5.9’讲话
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">新闻中心</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2014-05-12</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1985</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 35px"><span style="font-size: 19px;font-family: 宋体">5月12日晚19点30分，教育与艺术学部团总支学生会学习习近平‘5.9’讲话在我院体育楼二楼教室顺利展开。教育与艺术学部团总支学生会全体成员参加了本次学习。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 35px"><span style="font-family: 宋体; font-size: 19px; text-indent: 35px;">展望未来，我国青年一代必将大有可为，也必将大有作为。这是“长江后浪推前浪”的历史规律，也是“一代更比一代强”的青春责任。广大青年要勇敢肩负起时代赋予的重任，志存高远，脚踏实地，努力在实现中华民族伟大复兴的中国梦的生动实践中放飞青春梦想。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 28px;line-height: 35px"><span style="font-size: 19px;font-family: 宋体;letter-spacing: 1px">&nbsp;今年是习近平同志视察贵州大学并发表“5•9”重要讲话3周年。为牢记嘱托，以讲话精神激励广大师生自强不息，奋发进取，为学校发展、贵州发展、国家富强积极努力，为实现中华民族的伟大复兴贡献力量,为重温学习习近平同志“5•9”重要讲话精我学部开展了讨论会。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 35px"><span style="font-size: 19px;color: #2B2B2B;font-family: 宋体">讨论会上，与会代表纷纷结合自己的思想、工作、学习和生活实际，畅谈了学习习近平总书记五四讲话精神的感想、体会和收获。大家在发言中一致认为，总书记的讲话思想深刻，内涵丰富，语重心长，情真意切，既充分体现了党中央对青年的关怀、关心、关爱，又准确揭示了中国梦和青年梦的辩证关系，为当代青年健康成长指明了方向。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 28px;line-height: 35px"><span style="font-size: 19px;color: #2B2B2B;font-family: 宋体">与会代表还纷纷表示，要牢记总书记的深情嘱托，坚定理想信念，践行“五点希望”，把学习贯彻总书记讲话精神转化为立德树人、学习成才的强大动力和自觉行动，共享人生出彩机会。校团委副书记陈文说，广大团员青年要紧跟党中央的号召，热爱祖国，热爱人民，热爱民族，锤炼品格，敢于有梦、勇于追梦、勤于圆梦。应用学院党委副书记许宏志说，习总书记将青年人作为实现中国梦的重要力量提出来，自己深受鼓舞，更感责任重大，一定会更加努力做好本职工作，培养青年学子，为青春摆渡。校团委关福远老师说，作为共青团干部，要不断的学习新知识和新的工作方法，进一步为青年学生发展提供更多的平台。</span></p><p style="margin-top: 0;margin-bottom: 0;padding: 0px;text-indent: 37px;line-height: 35px"><span style="font-size: 19px;color: #2B2B2B;font-family: 宋体">在最后总结中说:习总书记的讲话高度概括了青年在实现中国梦这一历史进程中的地位，提出了青年所肩负的历史使命，充分体现了党和国家对当代青年的关怀和期望，为青年健康成长明确了方向。现在，全社会都在关注青年，关心青春话题，当代青年大学生，不仅要关注社会矛盾热点，更要为祖国的进步喝彩鼓掌；要按照习总书记的五点希望和要求，以研究的目光去学习，关心国家的大事小事，不断提高综合素质，为实现个人梦想和伟大的中国梦而努力奋斗。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
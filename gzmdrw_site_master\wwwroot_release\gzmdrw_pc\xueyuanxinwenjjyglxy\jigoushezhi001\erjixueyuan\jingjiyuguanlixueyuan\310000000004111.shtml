<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_2024届行政管理专业学生黔陶乡实习工作总结座谈会顺利召开</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000004111.shtml" class="text-decoration-none text-dark fw-bold">
                                                2024届行政管理专业学生黔陶乡实习工作总结座谈会顺利召开
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-04-08</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1640</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:仿宋_GB2312">3月28日，2024届行政管理专业学生黔陶乡实习工作总结座谈会在花溪区黔陶乡财政三楼会议室成功召开。参加本次总结会的有花溪区黔陶乡人民政府党政办主任黄敏、公共管理办公室主任李姣，贵阳人文科技学院经济与管理学院行政管理教研室主任邹晓抒及教师杨胜美、刘凌之、陈敏，以及2024届行政管理专业5名实习生。</span></span></p><p style="text-indent: 0px; text-align: center;"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 21px;background: #FFFFFF"><img src="/uploads/20240408/1f39136e6903e556c15499b220dbf9a5.jpg" alt="1.jpg"/>&nbsp;</span></p><p style="text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:仿宋_GB2312">会议首先由行政管理教研室主任邹晓抒发言，其对黔陶乡政府给予学院实习工作的支持表示感谢，同时也很欣慰同学们在实习工作中提升了实践能力，学会了在工作中如何与人沟通等。邹老师代表我院强烈希望未来有关实习合作能够长久进行下去。随后黔陶乡政府党政办主任李姣等对实习生的表现予以了肯定，感谢他们在实习工作中的付出。并对几位实习生今后在求职以及进入工作岗位后应注意的事项等给予了诸多中肯的建议。</span></span></p><p style="line-height: 33px; text-align: center;"><img src="/uploads/20240408/ccb3cdd89260f0842211821bcb6c2fd4.jpg" alt="2.jpg"/></p><p><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:仿宋_GB2312">&nbsp; &nbsp; 会上实习生积极踊跃发言，指出实际工作中学到的东西远比课本上的知识丰富，特别是团队合作和问题解决能力的提升等方面。实习生们意识到理论知识与实践相结合的重要性，并表示将所学应用于未来的学习和工作中。同时他们还表示，通过实习，他们不仅提升了专业技能，也增强了社会责任感和公共意识。</span></span></p><p style="text-align: center;"><img src="/uploads/20240408/ce7f2b0a8251e3999711e84cf526c102.jpg" alt="3.jpg"/></p><p style="text-indent: 37px;line-height: 33px"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 19px;background: #FFFFFF"><span style="font-family:仿宋_GB2312">本次实习取得了积极的成果，实习生们普遍认识到了实习经历对他们的专业成长和个人发展有着重要影响。期待未来实习生能够在此基础上继续提升，成为政府工作的有力支持，并在公共服务领域发挥更大的作用，谱写人生华丽新篇章。同时，通过与黔陶乡政府持续的实习合作，能够积极推进我院行政管理专业与政府部门</span><span style="font-family:仿宋_GB2312">“政教合作”的进一步发展，提升我院人才培养质量。</span></span></p><p style="text-align: center;"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 21px;background: #FFFFFF">&nbsp;<img src="/uploads/20240408/04f54bbef3a850177977cce6c86626c3.png" alt="4.png"/></span></p><p style=";text-align:right"><span style="font-family: 仿宋_GB2312;color: #333333;letter-spacing: 0;font-size: 16px;background: #FFFFFF"><span style="font-family:仿宋_GB2312">会后合影留念</span></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
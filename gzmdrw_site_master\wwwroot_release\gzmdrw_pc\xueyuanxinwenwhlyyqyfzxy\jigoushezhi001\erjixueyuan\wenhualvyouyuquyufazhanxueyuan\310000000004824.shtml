<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_我院赴黄果树开元名都酒店“访企拓岗”并看望实习生！</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000004824.shtml" class="text-decoration-none text-dark fw-bold">
                                                我院赴黄果树开元名都酒店“访企拓岗”并看望实习生！
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-19</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1534</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 2em; text-align: justify;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF; font-family: 宋体; color: #888888; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;">为进一步加强校企深度融合，开拓实习基地和就业岗位。5月18日，我院院长金颖若、副院长于金娜、旅游管理教研室副主任俞杰、21级旅游管理专业班主任杨青一行赴黄果树开元名都酒店“访企拓岗”并看望实习生。</span></p><p style="text-indent: 2em; text-align: justify;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF; font-family: 宋体; color: #888888; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;">我院一行与酒店总经理和人力资源部、餐饮部、房务部等部门负责人进行了座谈。校企双方分别介绍了学院发展情况与酒店概况，就岗位需求、实习和就业等情况进行了交流探讨。我院对酒店在实习生管理、培养方面的举措表示赞赏；酒店给予我院的实习教学管理和实习生高度评价，并表示，留下就业的三位实习生都将在近期获得升职。</span></span></p><p style="text-align:center"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.544px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><img src="/uploads/20240522/1dee2d3ef5644e0007a283e5798c22ab.jpg" alt="1.1.jpg"/></span></span></p><p style="text-align:center"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.544px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><img src="/uploads/20240522/7f8fbd323e64f1485e8248ef9e688770.jpg" alt="1.2.jpg" width="1080" height="663" border="0" vspace="0" style="width: 1080px; height: 663px;"/></span></span></p><p style="text-indent: 2em; text-align: justify;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.544px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF; font-family: 宋体; color: #888888; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;">最后，学院一行看望了实习生并告诉同学们，他们已在毕业前提前就业，不久之后的毕业典礼上将会以在校且在职的身份接受授予学位</span><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF; font-family: 宋体; color: #888888; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;">。学院与酒店将继续保持良好合作势头，推动校企合作向纵深发展。</span></span></span></p><p style="text-align:center"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; letter-spacing: 0.544px; text-align: justify; text-indent: 34px; background-color: #FFFFFF;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; letter-spacing: 0.5px; text-align: justify; text-indent: 34px; background-color: #FFFFFF; font-family: 宋体; color: #888888; line-height: 2em; box-sizing: border-box !important; overflow-wrap: break-word !important;"><img src="/uploads/20240522/e1718d28da784c642ccf090a1496e8b0.jpg" alt="1.3.jpg" width="1080" height="663" border="0" vspace="0" style="width: 1080px; height: 663px;"/></span></span></span></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
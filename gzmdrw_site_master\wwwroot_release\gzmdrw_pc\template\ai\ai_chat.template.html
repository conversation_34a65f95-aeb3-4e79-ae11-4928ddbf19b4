<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>文文AI助手</title>
    <style>
      @import url(https://fonts.googleapis.com/css2?family=Lato&display=swap);
      @import url(https://fonts.googleapis.com/css2?family=Open+Sans&display=swap);
      @import url(https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200);
      @import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css);

      /*! tailwindcss v3.4.11 | MIT License | https://tailwindcss.com*/
      *,

      :after,
      :before {
        border: 0 solid #e5e7eb;
        box-sizing: border-box;
      }
      :after,
      :before {
        --tw-content: "";
      }
      :host,
      html {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        font-family: Open Sans, ui-sans-serif, system-ui, sans-serif,
          Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
        font-feature-settings: normal;
        font-variation-settings: normal;
        -moz-tab-size: 4;
        tab-size: 4;
        -webkit-tap-highlight-color: transparent;
      }
      body {
        line-height: inherit;
        margin: 0;
      }
      hr {
        border-top-width: 1px;
        color: inherit;
        height: 0;
      }
      abbr:where([title]) {
        text-decoration: underline dotted;
      }
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        font-size: inherit;
        font-weight: inherit;
      }
      a {
        color: inherit;
        text-decoration: inherit;
      }
      b,
      strong {
        font-weight: bolder;
      }
      code,
      kbd,
      pre,
      samp {
        font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
          Liberation Mono, Courier New, monospace;
        font-feature-settings: normal;
        font-size: 1em;
        font-variation-settings: normal;
      }
      small {
        font-size: 80%;
      }
      sub,
      sup {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }
      sub {
        bottom: -0.25em;
      }
      sup {
        top: -0.5em;
      }
      table {
        border-collapse: collapse;
        border-color: inherit;
        text-indent: 0;
      }
      button,
      input,
      optgroup,
      select,
      textarea {
        color: inherit;
        font-family: inherit;
        font-feature-settings: inherit;
        font-size: 100%;
        font-variation-settings: inherit;
        font-weight: inherit;
        letter-spacing: inherit;
        line-height: inherit;
        margin: 0;
        padding: 0;
      }
      button,
      select {
        text-transform: none;
      }
      button,
      input:where([type="button"]),
      input:where([type="reset"]),
      input:where([type="submit"]) {
        -webkit-appearance: button;
        background-color: transparent;
        background-image: none;
      }
      :-moz-focusring {
        outline: auto;
      }
      :-moz-ui-invalid {
        box-shadow: none;
      }
      progress {
        vertical-align: baseline;
      }
      ::-webkit-inner-spin-button,
      ::-webkit-outer-spin-button {
        height: auto;
      }
      [type="search"] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
      }
      ::-webkit-search-decoration {
        -webkit-appearance: none;
      }
      ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }
      summary {
        display: list-item;
      }
      blockquote,
      dd,
      dl,
      figure,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      hr,
      p,
      pre {
        margin: 0;
      }
      fieldset {
        margin: 0;
      }
      fieldset,
      legend {
        padding: 0;
      }
      menu,
      ol,
      ul {
        list-style: none;
        margin: 0;
        padding: 0;
      }
      dialog {
        padding: 0;
      }
      textarea {
        resize: vertical;
      }
      input::placeholder,
      textarea::placeholder {
        color: #9ca3af;
        opacity: 1;
      }
      [role="button"],
      button {
        cursor: pointer;
      }
      :disabled {
        cursor: default;
      }
      audio,
      canvas,
      embed,
      iframe,
      img,
      object,
      svg,
      video {
        display: block;
        vertical-align: middle;
      }
      img,
      video {
        height: auto;
        max-width: 100%;
      }
      [hidden] {
        display: none;
      }
      *,
      :after,
      :before {
        --tw-border-spacing-x: 0;
        --tw-border-spacing-y: 0;
        --tw-translate-x: 0;
        --tw-translate-y: 0;
        --tw-rotate: 0;
        --tw-skew-x: 0;
        --tw-skew-y: 0;
        --tw-scale-x: 1;
        --tw-scale-y: 1;
        --tw-pan-x: ;
        --tw-pan-y: ;
        --tw-pinch-zoom: ;
        --tw-scroll-snap-strictness: proximity;
        --tw-gradient-from-position: ;
        --tw-gradient-via-position: ;
        --tw-gradient-to-position: ;
        --tw-ordinal: ;
        --tw-slashed-zero: ;
        --tw-numeric-figure: ;
        --tw-numeric-spacing: ;
        --tw-numeric-fraction: ;
        --tw-ring-inset: ;
        --tw-ring-offset-width: 0px;
        --tw-ring-offset-color: #fff;
        --tw-ring-color: #ad1e23;
        --tw-ring-offset-shadow: 0 0 #0000;
        --tw-ring-shadow: 0 0 #0000;
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000;
        --tw-blur: ;
        --tw-brightness: ;
        --tw-contrast: ;
        --tw-grayscale: ;
        --tw-hue-rotate: ;
        --tw-invert: ;
        --tw-saturate: ;
        --tw-sepia: ;
        --tw-drop-shadow: ;
        --tw-backdrop-blur: ;
        --tw-backdrop-brightness: ;
        --tw-backdrop-contrast: ;
        --tw-backdrop-grayscale: ;
        --tw-backdrop-hue-rotate: ;
        --tw-backdrop-invert: ;
        --tw-backdrop-opacity: ;
        --tw-backdrop-saturate: ;
        --tw-backdrop-sepia: ;
        --tw-contain-size: ;
        --tw-contain-layout: ;
        --tw-contain-paint: ;
        --tw-contain-style: ;
      }
      ::backdrop {
        --tw-border-spacing-x: 0;
        --tw-border-spacing-y: 0;
        --tw-translate-x: 0;
        --tw-translate-y: 0;
        --tw-rotate: 0;
        --tw-skew-x: 0;
        --tw-skew-y: 0;
        --tw-scale-x: 1;
        --tw-scale-y: 1;
        --tw-pan-x: ;
        --tw-pan-y: ;
        --tw-pinch-zoom: ;
        --tw-scroll-snap-strictness: proximity;
        --tw-gradient-from-position: ;
        --tw-gradient-via-position: ;
        --tw-gradient-to-position: ;
        --tw-ordinal: ;
        --tw-slashed-zero: ;
        --tw-numeric-figure: ;
        --tw-numeric-spacing: ;
        --tw-numeric-fraction: ;
        --tw-ring-inset: ;
        --tw-ring-offset-width: 0px;
        --tw-ring-offset-color: #fff;
        --tw-ring-color:#ad1e23;
        --tw-ring-offset-shadow: 0 0 #0000;
        --tw-ring-shadow: 0 0 #0000;
        --tw-shadow: 0 0 #0000;
        --tw-shadow-colored: 0 0 #0000;
        --tw-blur: ;
        --tw-brightness: ;
        --tw-contrast: ;
        --tw-grayscale: ;
        --tw-hue-rotate: ;
        --tw-invert: ;
        --tw-saturate: ;
        --tw-sepia: ;
        --tw-drop-shadow: ;
        --tw-backdrop-blur: ;
        --tw-backdrop-brightness: ;
        --tw-backdrop-contrast: ;
        --tw-backdrop-grayscale: ;
        --tw-backdrop-hue-rotate: ;
        --tw-backdrop-invert: ;
        --tw-backdrop-opacity: ;
        --tw-backdrop-saturate: ;
        --tw-backdrop-sepia: ;
        --tw-contain-size: ;
        --tw-contain-layout: ;
        --tw-contain-paint: ;
        --tw-contain-style: ;
      }
      #webcrumbs .mb-3 {
        margin-bottom: 12px;
      }
      #webcrumbs .mb-8 {
        margin-bottom: 32px;
      }
      #webcrumbs .mr-2 {
        margin-right: 8px;
      }
      #webcrumbs .mt-12 {
        margin-top: 48px;
      }
      #webcrumbs .mt-2 {
        margin-top: 8px;
      }
      #webcrumbs .mt-3 {
        margin-top: 12px;
      }
      #webcrumbs .inline-block {
        display: inline-block;
      }
      #webcrumbs .flex {
        display: flex;
      }
      #webcrumbs .hidden {
        display: none;
      }
      #webcrumbs .h-10 {
        height: 40px;
      }
      #webcrumbs .h-16 {
        height: 64px;
      }
      #webcrumbs .h-\[500px\] {
        height: 500px;
      }
      #webcrumbs .min-h-screen {
        min-height: 100vh;
      }
      #webcrumbs .w-10 {
        width: 40px;
      }
      #webcrumbs .w-16 {
        width: 64px;
      }
      #webcrumbs .w-\[1200px\] {
        width: 1200px;
      }
      #webcrumbs .w-full {
        width: 100%;
      }
      #webcrumbs .max-w-\[80\%\] {
        max-width: 80%;
      }
      #webcrumbs .flex-1 {
        flex: 1 1 0%;
      }
      #webcrumbs .flex-shrink-0 {
        flex-shrink: 0;
      }
      #webcrumbs .cursor-pointer {
        cursor: pointer;
      }
      #webcrumbs .list-decimal {
        list-style-type: decimal;
      }
      #webcrumbs .list-disc {
        list-style-type: disc;
      }
      #webcrumbs .flex-row {
        flex-direction: row;
      }
      #webcrumbs .flex-col {
        flex-direction: column;
      }
      #webcrumbs .flex-wrap {
        flex-wrap: wrap;
      }
      #webcrumbs .items-start {
        align-items: flex-start;
      }
      #webcrumbs .items-center {
        align-items: center;
      }
      #webcrumbs .justify-center {
        justify-content: center;
      }
      #webcrumbs .justify-between {
        justify-content: space-between;
      }
      #webcrumbs .gap-2 {
        gap: 8px;
      }
      #webcrumbs .gap-3 {
        gap: 12px;
      }
      #webcrumbs .gap-4 {
        gap: 16px;
      }
      #webcrumbs .gap-6 {
        gap: 24px;
      }
      #webcrumbs .gap-8 {
        gap: 32px;
      }
      #webcrumbs :is(.space-y-1 > :not([hidden]) ~ :not([hidden])) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(4px * var(--tw-space-y-reverse));
        margin-top: calc(4px * (1 - var(--tw-space-y-reverse)));
      }
      #webcrumbs :is(.space-y-2 > :not([hidden]) ~ :not([hidden])) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(8px * var(--tw-space-y-reverse));
        margin-top: calc(8px * (1 - var(--tw-space-y-reverse)));
      }
      #webcrumbs :is(.space-y-3 > :not([hidden]) ~ :not([hidden])) {
        --tw-space-y-reverse: 0;
        margin-bottom: calc(12px * var(--tw-space-y-reverse));
        margin-top: calc(12px * (1 - var(--tw-space-y-reverse)));
      }
      #webcrumbs .self-end {
        align-self: flex-end;
      }
      #webcrumbs .overflow-hidden {
        overflow: hidden;
      }
      #webcrumbs .overflow-y-auto {
        overflow-y: auto;
      }
      #webcrumbs .rounded-2xl {
        border-radius: 48px;
      }
      #webcrumbs .rounded-full {
        border-radius: 9999px;
      }
      #webcrumbs .rounded-lg {
        border-radius: 24px;
      }
      #webcrumbs .rounded-xl {
        border-radius: 36px;
      }
      #webcrumbs .rounded-tl-none {
        border-top-left-radius: 0;
      }
      #webcrumbs .rounded-tr-none {
        border-top-right-radius: 0;
      }
      #webcrumbs .border {
        border-width: 1px;
      }
      #webcrumbs .border-t {
        border-top-width: 1px;
      }
      #webcrumbs .border-gray-300 {
        --tw-border-opacity: 1;
        border-color: rgb(209 213 219 / var(--tw-border-opacity));
      }
      #webcrumbs .bg-gray-100 {
        --tw-bg-opacity: 1;
        background-color: rgb(243 244 246 / var(--tw-bg-opacity));
      }
      #webcrumbs .bg-gray-200 {
        --tw-bg-opacity: 1;
        background-color: rgb(229 231 235 / var(--tw-bg-opacity));
      }
      #webcrumbs .bg-gray-300 {
        --tw-bg-opacity: 1;
        background-color: rgb(209 213 219 / var(--tw-bg-opacity));
      }
      #webcrumbs .bg-primary-100 {
        --tw-bg-opacity: 1;
        background-color: rgba(173, 30, 35, 0.1); /* 浅红 */
        color: rgba(0, 0, 0, 0.9) !important;
      }
      #webcrumbs .bg-primary-500 {
        --tw-bg-opacity: 1;
        background-color: #ad1e23;
        color: hsla(0, 0%, 100%, 0.9) !important;
      }
      #webcrumbs .bg-primary-600 {
        --tw-bg-opacity: 1;
        background-color: #99171c;
        color: hsla(0, 0%, 100%, 0.9) !important;
      }
      #webcrumbs .bg-white {
        --tw-bg-opacity: 1;
        background-color: rgb(255 255 255 / var(--tw-bg-opacity));
      }
      #webcrumbs .bg-white\/20 {
        background-color: hsla(0, 0%, 100%, 0.2);
      }
      #webcrumbs .bg-gradient-to-br {
        background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
      }
      #webcrumbs .bg-gradient-to-r {
        background-image: linear-gradient(to right, var(--tw-gradient-stops));
      }
      #webcrumbs .from-blue-50 {
        --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
        --tw-gradient-to: rgba(239, 246, 255, 0) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
      }
      #webcrumbs .from-primary-100 {
        --tw-gradient-from: rgba(173, 30, 35, 0.1) var(--tw-gradient-from-position);
        --tw-gradient-to: rgba(173, 30, 35, 0.05) var(--tw-gradient-to-position);
        --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
      }
      #webcrumbs .to-blue-600 {
        --tw-gradient-to: #a76f66 var(--tw-gradient-to-position);
      }
      #webcrumbs .to-primary-50 {
        --tw-gradient-to: rgba(173, 30, 35, 0.05) var(--tw-gradient-to-position);
      }
      #webcrumbs .p-2 {
        padding: 8px;
      }
      #webcrumbs .p-4 {
        padding: 16px;
      }
      #webcrumbs .p-5 {
        padding: 20px;
      }
      #webcrumbs .p-6 {
        padding: 24px;
      }
      #webcrumbs .p-8 {
        padding: 32px;
      }
      #webcrumbs .px-3 {
        padding-left: 12px;
        padding-right: 12px;
      }
      #webcrumbs .px-4 {
        padding-left: 16px;
        padding-right: 16px;
      }
      #webcrumbs .py-1 {
        padding-bottom: 4px;
        padding-top: 4px;
      }
      #webcrumbs .py-2 {
        padding-bottom: 8px;
        padding-top: 8px;
      }
      #webcrumbs .pl-5 {
        padding-left: 20px;
      }
      #webcrumbs .text-center {
        text-align: center;
      }
      #webcrumbs .font-sans {
        font-family: Open Sans, ui-sans-serif, system-ui, sans-serif,
          Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
      }
      #webcrumbs .text-2xl {
        font-size: 24px;
        line-height: 31.200000000000003px;
      }
      #webcrumbs .text-3xl {
        font-size: 30px;
        line-height: 36px;
      }
      #webcrumbs .text-sm {
        font-size: 14px;
        line-height: 21px;
      }
      #webcrumbs .text-xl {
        font-size: 20px;
        line-height: 28px;
      }
      #webcrumbs .font-bold {
        font-weight: 700;
      }
      #webcrumbs .font-semibold {
        font-weight: 600;
      }
      #webcrumbs .text-gray-600 {
        --tw-text-opacity: 1;
        color: rgb(75 85 99 / var(--tw-text-opacity));
      }
      #webcrumbs .text-gray-700 {
        --tw-text-opacity: 1;
        color: rgb(55 65 81 / var(--tw-text-opacity));
      }
      #webcrumbs .text-primary-500 {
        --tw-text-opacity: 1;
        color: #ad1e23;
      }
      #webcrumbs .text-primary-600 {
        --tw-text-opacity: 1;
        color: #99171c;
      }
      #webcrumbs .text-primary-700 {
        --tw-text-opacity: 1;
        color: #891317;
      }
      #webcrumbs .text-white {
        --tw-text-opacity: 1;
        color: rgb(255 255 255 / var(--tw-text-opacity));
      }
      #webcrumbs .shadow-lg {
        --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -4px rgba(0, 0, 0, 0.1);
        --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
          0 4px 6px -4px var(--tw-shadow-color);
      }
      #webcrumbs .shadow-lg,
      #webcrumbs .shadow-md {
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
          var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
      }
      #webcrumbs .shadow-md {
        --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -2px rgba(0, 0, 0, 0.1);
        --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),
          0 2px 4px -2px var(--tw-shadow-color);
      }
      #webcrumbs .shadow-xl {
        --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 8px 10px -6px rgba(0, 0, 0, 0.1);
        --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
          0 8px 10px -6px var(--tw-shadow-color);
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
          var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
      }
      #webcrumbs .transition-all {
        transition-duration: 0.15s;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      }
      #webcrumbs .transition-colors {
        transition-duration: 0.15s;
        transition-property: color, background-color, border-color,
          text-decoration-color, fill, stroke;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      }
      #webcrumbs .transition-shadow {
        transition-duration: 0.15s;
        transition-property: box-shadow;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      }
      #webcrumbs .duration-300 {
        transition-duration: 0.3s;
      }
      #webcrumbs {
        font-family: Open Sans !important;
        font-size: 16px !important;
      }
      #webcrumbs :is(.bg-primary-100) {
        color: rgba(0, 0, 0, 0.9) !important;
      }
      #webcrumbs :is(.bg-primary-500) {
        color: hsla(0, 0%, 100%, 0.9) !important;
      }
      #webcrumbs :is(.bg-primary-600) {
        color: hsla(0, 0%, 100%, 0.9) !important;
      }
      #webcrumbs .hover\:-translate-y-1:hover {
        --tw-translate-y: -4px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
          rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
          scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      }
      #webcrumbs .hover\:bg-gray-300:hover {
        --tw-bg-opacity: 1;
        background-color: rgb(209 213 219 / var(--tw-bg-opacity));
      }
      #webcrumbs .hover\:bg-primary-200:hover {
        --tw-bg-opacity: 1;
        background-color: rgba(173, 30, 35, 0.2); /* hover 浅红 */
      }
      #webcrumbs .hover\:bg-primary-50:hover {
        --tw-bg-opacity: 1;
        background-color: rgba(173, 30, 35, 0.05); /* hover 更浅红 */
      }
      #webcrumbs .hover\:bg-primary-600:hover {
        --tw-bg-opacity: 1;
        background-color: #99171c;
      }
      #webcrumbs .hover\:bg-white\/30:hover {
        background-color: hsla(0, 0%, 100%, 0.3);
      }
      #webcrumbs .hover\:text-primary-600:hover {
        --tw-text-opacity: 1;
        color: #99171c;
      }
      #webcrumbs .hover\:text-primary-800:hover {
        --tw-text-opacity: 1;
        color: #6e1014; /* 更深的红 */
      }
      #webcrumbs .hover\:underline:hover {
        text-decoration-line: underline;
      }
      #webcrumbs .hover\:shadow-lg:hover {
        --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -4px rgba(0, 0, 0, 0.1);
        --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
          0 4px 6px -4px var(--tw-shadow-color);
      }
      #webcrumbs .hover\:shadow-lg:hover,
      #webcrumbs .hover\:shadow-xl:hover {
        box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
          var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
      }
      #webcrumbs .hover\:shadow-xl:hover {
        --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 8px 10px -6px rgba(0, 0, 0, 0.1);
        --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
          0 8px 10px -6px var(--tw-shadow-color);
      }
      #webcrumbs .focus\:outline-none:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      #webcrumbs .focus\:ring-2:focus {
        --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
          var(--tw-ring-offset-width) var(--tw-ring-offset-color);
        --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
          calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
        box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
          var(--tw-shadow, 0 0 #0000);
      }
      #webcrumbs .focus\:ring-primary-500:focus {
        --tw-ring-opacity: 1;
        --tw-ring-color: #ad1e23;
      }
      #webcrumbs input:focus {
        border-color: #ad1e23;
        box-shadow: 0 0 0 2px rgba(173, 30, 35, 0.2);
      }
      @media (min-width: 768px) {
        #webcrumbs .md\:flex {
          display: flex;
        }
        #webcrumbs .md\:hidden {
          display: none;
        }
      }
      @media (min-width: 1024px) {
        #webcrumbs .lg\:w-1\/3 {
          width: 33.333333%;
        }
        #webcrumbs .lg\:w-2\/3 {
          width: 66.666667%;
        }
        #webcrumbs .lg\:flex-row {
          flex-direction: row;
        }
      }

      body {
        line-height: inherit;
        padding: 24px;
        display: flex;
        flex-direction: column;
        min-width: 100vw;
        min-height: 100vh;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #ffffff, #f8f8f8);
      }

      /* 修改 */
      /* 保持原有布局特征 */
      /* 关键修复 */
      body {
        /* 原样式保留 */
        padding: 24px 0; /* 移除水平 padding */
        width: 100vw;
        overflow-x: hidden; /* 防止边缘溢出 */
      }

      /* 新增响应式处理 */
      @media (max-width: 1200px) {
        #webcrumbs {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }

      /* 修复图片溢出问题 */
      #webcrumbs img {
        max-width: 100%;
        height: auto;
      }

      /* 确保导航栏响应式 */
      @media (max-width: 767px) {
        #webcrumbs header {
          flex-wrap: wrap;
          gap: 1rem;
        }
      }

      /* 聊天气泡框优化 */
      #webcrumbs .chat-message {
        max-width: 80%;
        word-wrap: break-word;
        white-space: pre-wrap;
        overflow-wrap: break-word;
      }

      /* 参考来源样式优化 */
      #webcrumbs .source-list {
        margin-top: 12px;
        list-style: none;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      #webcrumbs .source-item {
        position: relative;
        padding: 6px 10px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        height: 32px;
        overflow: visible;
        flex: 0 1 auto;
        min-width: 120px;
        max-width: fit-content;
        z-index: 1;
      }

      #webcrumbs .source-item:hover {
        background: rgba(255, 255, 255, 0.8);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        z-index: 2;
      }

      #webcrumbs .source-number {
        font-weight: 600;
        color: #ad1e23;
        margin-right: 6px;
        flex-shrink: 0;
        font-size: 13px;
      }

      #webcrumbs .source-title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #4b5563;
        font-size: 13px;
        max-width: 100px;
      }

      #webcrumbs .source-title a {
        color: inherit;
        text-decoration: none;
        transition: color 0.2s ease;
      }

      #webcrumbs .source-title a:hover {
        color: #ad1e23;
      }

      #webcrumbs .source-detail {
        position: absolute;
        top: calc(100% + 8px);
        left: 0;
        width: 360px;
        background: white;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 3;
        transform: translateY(-4px) scale(0.98);
        transform-origin: top left;
        pointer-events: none;
      }

      #webcrumbs .source-detail.visible {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
        pointer-events: auto;
      }

      #webcrumbs .source-detail::before {
        content: '';
        position: absolute;
        top: -6px;
        left: 20px;
        width: 12px;
        height: 12px;
        background: white;
        transform: rotate(45deg);
        box-shadow: -2px -2px 2px rgba(0, 0, 0, 0.05);
      }

      #webcrumbs .source-detail-title {
        font-weight: 600;
        color: #ad1e23;
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      }

      #webcrumbs .source-detail-content {
        font-size: 14px;
        line-height: 1.5;
        color: #4b5563;
        margin-bottom: 12px;
        max-height: 200px;
        overflow-y: auto;
        padding-right: 8px;
      }

      /* 自定义滚动条样式 */
      #webcrumbs .source-detail-content::-webkit-scrollbar {
        width: 4px;
      }

      #webcrumbs .source-detail-content::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 2px;
      }

      #webcrumbs .source-detail-content::-webkit-scrollbar-thumb {
        background: rgba(173, 30, 35, 0.3);
        border-radius: 2px;
      }

      #webcrumbs .source-detail-content::-webkit-scrollbar-thumb:hover {
        background: rgba(173, 30, 35, 0.5);
      }

      #webcrumbs .source-detail-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #6b7280;
        padding-top: 8px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
      }

      #webcrumbs .source-detail-link {
        color: #ad1e23;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        padding: 4px 8px;
        border-radius: 4px;
        background: rgba(173, 30, 35, 0.1);
      }

      #webcrumbs .source-detail-link:hover {
        color: #99171c;
        background: rgba(173, 30, 35, 0.2);
      }

      /* 移动端适配 */
      @media (max-width: 768px) {
        #webcrumbs .source-list {
          gap: 6px;
        }

        #webcrumbs .source-item {
          padding: 4px 8px;
          height: 28px;
          min-width: 100px;
          cursor: pointer; /* 恢复指针样式 */
          transition: background-color 0.2s ease;
        }

        #webcrumbs .source-title {
          font-size: 12px;
          max-width: 180px;
        }

        #webcrumbs .source-number {
          font-size: 12px;
        }

        /* 在移动端隐藏详情页相关内容 */
        #webcrumbs .source-detail {
          display: none;
        }

        /* 移动端点击效果 */
        #webcrumbs .source-item:active {
          background: rgba(173, 30, 35, 0.1);
        }
      }

      /* 桌面端聊天气泡框优化 */
      @media (min-width: 1024px) {
        #webcrumbs .chat-message {
          max-width: 60%;
          max-width: 600px;
        }
        
        #webcrumbs .chat-message p {
          line-height: 1.6;
          font-size: 15px;
        }
      }

      /* 移动端聊天气泡框优化 */
      @media (max-width: 1023px) {
        #webcrumbs .chat-message {
          max-width: 85%;
        }

        #webcrumbs .chat-message p {
          line-height: 1.5;
          font-size: 14px;
        }
      }

      /* Markdown 内容样式 */
      #webcrumbs .markdown-content {
        line-height: 1.6;
        color: inherit;
      }

      #webcrumbs .markdown-content h1,
      #webcrumbs .markdown-content h2,
      #webcrumbs .markdown-content h3,
      #webcrumbs .markdown-content h4,
      #webcrumbs .markdown-content h5,
      #webcrumbs .markdown-content h6 {
        margin: 16px 0 8px 0;
        font-weight: 600;
        color: #1f2937;
      }

      #webcrumbs .markdown-content h1 { font-size: 1.5em; }
      #webcrumbs .markdown-content h2 { font-size: 1.3em; }
      #webcrumbs .markdown-content h3 { font-size: 1.2em; }
      #webcrumbs .markdown-content h4 { font-size: 1.1em; }
      #webcrumbs .markdown-content h5 { font-size: 1em; }
      #webcrumbs .markdown-content h6 { font-size: 0.9em; }

      #webcrumbs .markdown-content p {
        margin: 8px 0;
        line-height: 1.6;
      }

      #webcrumbs .markdown-content ul,
      #webcrumbs .markdown-content ol {
        margin: 8px 0;
        padding-left: 20px;
      }

      #webcrumbs .markdown-content li {
        margin: 4px 0;
        line-height: 1.5;
      }

      #webcrumbs .markdown-content blockquote {
        margin: 12px 0;
        padding: 8px 16px;
        border-left: 4px solid #ad1e23;
        background-color: rgba(173, 30, 35, 0.05);
        font-style: italic;
      }

      #webcrumbs .markdown-content code {
        background-color: rgba(0, 0, 0, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Courier New', Courier, monospace;
        font-size: 0.9em;
      }

      #webcrumbs .markdown-content pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        margin: 12px 0;
        overflow-x: auto;
      }

      #webcrumbs .markdown-content pre code {
        background-color: transparent;
        padding: 0;
        border-radius: 0;
        font-size: 0.85em;
      }

      #webcrumbs .markdown-content table {
        border-collapse: collapse;
        width: 100%;
        margin: 12px 0;
        font-size: 0.9em;
      }

      #webcrumbs .markdown-content th,
      #webcrumbs .markdown-content td {
        border: 1px solid #ddd;
        padding: 8px 12px;
        text-align: left;
      }

      #webcrumbs .markdown-content th {
        background-color: #f8f9fa;
        font-weight: 600;
      }

      #webcrumbs .markdown-content a {
        color: #ad1e23;
        text-decoration: none;
      }

      #webcrumbs .markdown-content a:hover {
        text-decoration: underline;
      }

      #webcrumbs .markdown-content strong {
        font-weight: 600;
      }

      #webcrumbs .markdown-content em {
        font-style: italic;
      }

      #webcrumbs .markdown-content hr {
        border: none;
        border-top: 1px solid #e5e7eb;
        margin: 16px 0;
      }

    </style>
   
  </head>
  <body>
    <div
      id="webcrumbs"
      class="w-full max-w-[1200px] min-h-screen mx-auto bg-gradient-to-br from-blue-50 to-primary-50 p-8 font-sans flex flex-col items-center"
    >
      <header class="flex justify-between items-center mb-8">
        <div class="flex items-center gap-4">
          <img
            src="${Prefix}images/gzmdrw_logo.png"
            alt="Logo"
            class="h-16 w-16 rounded-full shadow-md hover:shadow-lg transition-all duration-300"
            keywords="guiyang college, logo, education"
          />
          <a href="https://www.gzmdrw.cn/">
            <img src="${Prefix}images/rw_logo_red.png" class="h-16 transition-all duration-300">
          </a>
        </div>
        <nav class="hidden md:flex items-center gap-6">
          <a
            href="https://www.gzmdrw.cn/home/"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            >首页</a
          >
          <a
            href="https://www.gzmdrw.cn/tushuguan_subsite/jigoushezhi001/jiguanbumen001/"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            >图书馆</a
          >
          <a
            href="https://www.gzmdrw.cn/xiaochangxinxiang/"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            >校长书记信箱</a
          >
          <a
            href="https://www.720yun.com/vr/433jzgmuum5"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            >3D全景校园</a
          >
          <a
            href="https://gzmyrw.jw.chaoxing.com/admin/login"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            >教务系统</a
          >
          <a
            href="https://www.gzmdrw.cn/admin/login?redirect=%2Findex"
            class="text-black hover:text-primary-600 hover:-translate-y-1 transition-all duration-300"
            > 管理员</a
          >
        </nav>
        <button
          class="md:hidden rounded-full p-2 bg-primary-100 hover:bg-primary-200 transition-colors"
        >
          <span class="material-symbols-outlined">menu</span>
        </button>
      </header>

      <main class="flex flex-col lg:flex-row gap-8">
        <section
          class="w-full lg:w-2/3 bg-white rounded-2xl shadow-xl overflow-hidden"
        >
          <div class="bg-primary-100 p-4">
            <h2 class="text-2xl font-bold text-center text-primary-700">
              文文AI 智能助手（我现在四岁，勉强学会说话，我会好好加油的！）
            </h2>
          </div>
          <div class="h-[500px] p-6 overflow-y-auto flex flex-col gap-4">
            <template v-for="(message, index) in messages" :key="index">
              <div class="flex items-start gap-3" :class="{'self-end': message.type === 'user'}">
                <div v-if="message.type === 'ai'" class="w-10 h-10 rounded-full border-4 border-[#ad1e23] flex items-center justify-center flex-shrink-0">
                  <img src="${Prefix}images/ww.png" alt="" class="w-full h-full rounded-full object-cover" />
                </div>
                <div
                  :class="[
                    message.type === 'ai' ? 'bg-primary-100 rounded-2xl rounded-tl-none' : 'bg-gray-100 rounded-2xl rounded-tr-none',
                    'p-4 w-[80%] break-words chat-message'
                  ]"
                >
                  <div
                    v-if="message.type === 'ai'"
                    class="markdown-content"
                    v-html="renderMarkdown(message.content)"
                  ></div>
                  <p
                    v-else
                    class="whitespace-pre-wrap"
                  >{{ message.content }}</p>
                  <div v-if="message.type === 'ai' && message.sources && message.sources.length > 0" class="mt-3 space-y-2 text-sm">
                    <h4 class="font-semibold text-primary-700">参考来源：</h4>
                    <ul class="source-list">
                      <li v-for="(source, idx) in message.sources" 
                          :key="idx" 
                          class="source-item"
                          @mouseenter="!isMobile && handleSourceHover(source, true)"
                          @mouseleave="!isMobile && handleSourceHover(source, false)"
                          @click="!isMobile && handleSourceClick(source)">
                        <span class="source-number">[{{ idx + 1 }}]</span>
                        <span class="source-title">
                          <a v-if="source.url" 
                             :href="source.url"
                             target="_blank"
                             rel="noopener noreferrer"
                             @click.stop="isMobile ? null : $event.preventDefault()">
                            {{ source.filename }}
                          </a>
                          <span v-else>{{ source.filename }}</span>
                        </span>
                        <div v-if="!isMobile" 
                             class="source-detail" 
                             :class="{ 'visible': source.showDetail }"
                             @mouseenter="clearSourceTimeout(source)"
                             @mouseleave="handleSourceHover(source, false)">
                          <h5 class="source-detail-title">{{ source.filename }}</h5>
                          <p class="source-detail-content">{{ source.content }}</p>
                          <div class="source-detail-meta">
                            <span>相似度: {{ (source.score * 100).toFixed(1) }}%</span>
                            <a v-if="source.url" 
                               :href="source.url" 
                               target="_blank" 
                               rel="noopener noreferrer"
                               class="source-detail-link"
                               @click.stop>
                              <span class="material-symbols-outlined" style="font-size: 16px;">link</span>
                              访问链接
                            </a>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
                <div v-if="message.type === 'user'" class="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                  <img src="${Prefix}images/men.jpg" alt="" class="w-full h-full rounded-full object-cover" />
                </div>
              </div>
            </template>
            <div v-if="isLoading" class="flex items-start gap-3">
              <div class="w-10 h-10 rounded-full border-4 border-[#ad1e23] flex items-center justify-center flex-shrink-0">
                <img src="${Prefix}images/ww.png" alt="" class="w-full h-full rounded-full object-cover" />
              </div>
              <div class="bg-primary-100 rounded-2xl rounded-tl-none p-4">
                <p>正在思考中...</p>
              </div>
            </div>
          </div>
          <div class="border-t p-4">
            <div class="flex gap-2">
              <input
                v-model="userInput"
                @keyup.enter="sendMessage()"
                @input="console.log('Input event:', $event.target.value)"
                type="text"
                placeholder="请输入你的问题..."
                class="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-all"
              />
              <button
                @click="sendMessage()"
                class="w-10 h-10 rounded-full bg-primary-500 hover:bg-primary-600 transition-colors flex items-center justify-center"
              >
                <span class="material-symbols-outlined text-white">send</span>
              </button>
              <button
                @click="clearMessages()"
                class="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 transition-colors flex items-center justify-center"
              >
                <span class="material-symbols-outlined">refresh</span>
              </button>
            </div>
            <div class="mt-2 flex flex-wrap gap-2">
              <button
                v-for="topic in ['专业设置', '学费信息', '奖学金政策', '校园活动']"
                :key="topic"
                @click="handleQuickReply(topic)"
                class="bg-primary-100 hover:bg-primary-200 rounded-full px-3 py-1 text-sm transition-colors"
              >
                {{ topic }}
              </button>
            </div>
          </div>
        </section>

        <aside class="w-full lg:w-1/3 flex flex-col gap-6">
          <article
            class="bg-white rounded-xl shadow-lg p-5 hover:shadow-xl transition-shadow"
          >
            <h3
              class="text-xl font-bold mb-3 text-primary-700 flex items-center"
            >
              <span class="material-symbols-outlined mr-2">school</span>学院简介
            </h3>
            <p class="text-gray-700">
              贵阳人文科技学院是一所集人文与科技为一体的综合性高等院校，致力于培养具有创新精神和实践能力的高素质人才。
            </p>
            <@cms_catalog alias="xuexiaojianjie" level="self">
              <#list DataList as c>
                <a href="${c.link}" class="text-primary-600 hover:text-primary-800 transition-colors">
                  <p class="font-semibold">了解更多</p>
                </a>
              </#list>
            </@cms_catalog>
          </article>
          <article
            class="bg-white rounded-xl shadow-lg p-5 hover:shadow-xl transition-shadow"
          >
            <h3
              class="text-xl font-bold mb-3 text-primary-700 flex items-center"
            >
              <span class="material-symbols-outlined mr-2">landscape</span
              >常用栏目
            </h3>
            <ul class="space-y-3">
              <@cms_catalog alias="news" level="Child">
                <#list DataList as c>
                  <li class="flex items-center gap-2">
                    <a href="${c.link}" class="text-primary-600 hover:text-primary-800 transition-colors">
                      <p class="font-semibold">${c.name}</p>
                    </a>
                  </li>
                </#list>
              </@cms_catalog>
            </ul>
          </article>
          <article
            class="bg-gradient-to-r from-primary-100 to-primary-50 rounded-xl shadow-lg p-5 text-primary-700 hover:shadow-xl transition-shadow"
          >
            <h3 class="text-xl font-bold mb-3 flex items-center">
              <span class="material-symbols-outlined mr-2">contact_support</span
              >学校信息
            </h3>
            <ul class="space-y-2">
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">call</span>
                <p>0851-88308620</p>
              </li>
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">mail</span>
                <p><EMAIL></p>
              </li>
              <li class="flex items-center gap-2">
                <span class="material-symbols-outlined">location_on</span>
                <p>贵州省贵阳市花溪区花溪大学城<br>贵阳人文科技学院大学城校区</p>
              </li>
            </ul>
            <div class="mt-3 flex gap-3">
              <img src="${Prefix}images/enroll_code_qr.jpg" alt="">
            </div>
          </article>
        </aside>
      </main>

      <footer class="mt-12 text-center text-gray-600 text-sm">
        <p>© 2023 贵阳人文科技学院 - 版权所有</p>
        <div class="mt-2 flex justify-center gap-4">
          <a href="#" class="hover:text-primary-600 transition-colors"
            >隐私政策</a
          >
          <a href="#" class="hover:text-primary-600 transition-colors"
            >使用条款</a
          >
          <a href="#" class="hover:text-primary-600 transition-colors"
            >网站地图</a
          >
        </div>
      </footer>
    </div>
    <!-- 1. 引入 Vue 3 -->
    <!-- <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script> -->
     <script src="${Prefix}js/vue_global.js"></script>

    <!-- 2. 引入 Axios -->
    <!-- <script src="https://unpkg.com/axios/dist/axios.min.js"></script> -->
    <script src="${Prefix}js/axios_min.js"></script>

    <!-- 3. 引入 Marked.js (Markdown 解析器) -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
  const { createApp, ref, onMounted } = Vue;

  createApp({
    setup() {
      const userInput = ref('');
      const messages = ref([
        { type: 'ai', content: '你好！我是贵阳人文科技学院的智能助手**"文文"**，致力于为师生提供便捷的信息服务。\n\n我现在支持 **Markdown 格式**了！可以显示：\n\n- **粗体文字**\n- *斜体文字*\n- `代码片段`\n- [链接](https://www.example.com)\n\n> 引用文字也可以正常显示\n\n请随时向我提问！' }
      ]);
      const isLoading = ref(false);
      const searchInput = ref('');
      const isMobile = ref(false);

      // 添加清空消息的方法
      const clearMessages = () => {
        messages.value = [
          { type: 'ai', content: '你好！我是贵阳人文科技学院的智能助手**"文文"**，致力于为师生提供便捷的信息服务。\n\n我现在支持 **Markdown 格式**了！可以显示：\n\n- **粗体文字**\n- *斜体文字*\n- `代码片段`\n- [链接](https://www.example.com)\n\n> 引用文字也可以正常显示\n\n请随时向我提问！' }
        ];
      };

      // Markdown 渲染方法
      const renderMarkdown = (content) => {
        if (!content) return '';
        try {
          // 配置 marked 选项
          marked.setOptions({
            breaks: true,        // 支持换行符转换为 <br>
            gfm: true,          // 启用 GitHub Flavored Markdown
            sanitize: false,    // 不进行 HTML 清理（如果需要安全性，可以设置为 true）
            smartLists: true,   // 智能列表
            smartypants: true   // 智能标点符号
          });

          return marked.parse(content);
        } catch (error) {
          console.error('Markdown 渲染错误:', error);
          return content; // 如果渲染失败，返回原始内容
        }
      };

      // 消息函数
      const sendMessage = async () => {
        const question = userInput.value.trim();
        if (!question) return;
        
        messages.value.push({ type: 'user', content: question });
        userInput.value = '';
        isLoading.value = true;

        try {
          const res = await axios.post('http://localhost:8000/api/query', {
            query: question,
            max_results: 3,
            similarity_threshold: 0.7
          });
          if (res.data && res.data.answer) {
            const sourcesWithProps = res.data.sources.map(createSourceWithProps);
            messages.value.push({
              type: 'ai',
              content: res.data.answer,
              sources: sourcesWithProps
            });
          } else {
            messages.value.push({ type: 'ai', content: '未获取到有效回复。' });
          }
        } catch (e) {
          messages.value.push({ type: 'ai', content: '请求失败，请稍后重试。' });
        } finally {
          isLoading.value = false;
        }
      };

      // 快捷回复
      const handleQuickReply = (topic) => {
        userInput.value = topic;
        sendMessage();
      };

      // 键盘事件
      const handleEnter = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      };

      // 在 setup() 函数中添加以下方法
      const handleSourceHover = (source, isEnter) => {
        if (isEnter) {
          clearSourceTimeout(source);
          source.showDetail = true;
        } else {
          source.closeTimeout = setTimeout(() => {
            source.showDetail = false;
          }, 80);
        }
      };

      const clearSourceTimeout = (source) => {
        if (source.closeTimeout) {
          clearTimeout(source.closeTimeout);
          source.closeTimeout = null;
        }
      };

      const handleSourceClick = (source) => {
        // 在桌面端点击时不触发
        if (!isTouchDevice()) return;
        
        source.showDetail = !source.showDetail;
        // 关闭其他打开的详情
        messages.value.forEach(message => {
          if (message.sources) {
            message.sources.forEach(s => {
              if (s !== source) s.showDetail = false;
            });
          }
        });
      };

      const handleSourceTouch = (source) => {
        // 防止触摸设备上的链接立即打开
        event.preventDefault();
        handleSourceClick(source);
      };

      const isTouchDevice = () => {
        return ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
      };

      // 在创建消息时初始化属性
      const createSourceWithProps = (source) => {
        return {
          ...source,
          showDetail: false,
          closeTimeout: null
        };
      };

      onMounted(() => {
        const checkMobile = () => {
          isMobile.value = window.innerWidth <= 768;
        };
        
        checkMobile();
        window.addEventListener('resize', checkMobile);
        
        return () => {
          window.removeEventListener('resize', checkMobile);
        };
      });

      return {
        userInput,
        messages,
        isLoading,
        sendMessage,
        handleQuickReply,
        searchInput,
        handleEnter,
        clearMessages,
        renderMarkdown,
        handleSourceHover,
        clearSourceTimeout,
        handleSourceClick,
        handleSourceTouch,
        isTouchDevice,
        isMobile
      };
    }
  }).mount('#webcrumbs');
</script>
  </body>
</html>

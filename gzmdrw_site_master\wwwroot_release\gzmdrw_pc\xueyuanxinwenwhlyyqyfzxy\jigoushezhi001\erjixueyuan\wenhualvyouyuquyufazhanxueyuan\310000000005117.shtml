<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_“学创杯”创业综合模拟大赛来啦！</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000005117.shtml" class="text-decoration-none text-dark fw-bold">
                                                “学创杯”创业综合模拟大赛来啦！
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-30</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1378</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">各学院及相关单位：为深入贯彻习近平总书记在全国教育大会的重要讲话精神，落实《国务院办公厅关于进一步支持大学生创新创业的指导意见》（国办发[2021]35号），根据《关于举办第十一届“学创杯”全国大学生创业综合模拟演训活动的通知》、《关于举办第十一届“学创杯”全国大学生创业综合模拟大赛贵州省选拔赛的通知》，为推进我校创新创业教育，鼓励学生自主创业。经研究，决定组织我校学生参加第十一届“学创杯”全国大学生创业综合模拟大赛并举办贵阳人文科技学院“学创杯”创业综合模拟大赛，现将有关事宜通知如下：</span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">组织管理</span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">主办单位<br/></span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">文化旅游与区域发展学院</span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">经济与管理学院</span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">技术支持</span></p><p style="text-align: center; line-height: 2em; text-indent: 0em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">杭州贝腾科技有限公司</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">参赛对象</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">我校具有学籍的全日制在校本科生，每个团队由1-3名学生、1-2名指导老师组成。每名学生限参加1个团队，鼓励跨学科组队，不能跨校组队。财务决策模拟赛项仅限于财务管理的学生参加，且指导老师须为财务管理的老师。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">竞赛内容及规则</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">比赛分为创业综合模拟、数字营销模拟以及财务决策模拟三个赛项，分别竞赛。各赛项具体内容及规则详见附件1、附件2、附件3。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">竞赛流程</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（一）报名各参赛团队于2024年6月19日前进入学校“学创杯”赛事钉钉群填写报名表并归档至指定的文件夹进行报名，加入比赛通知钉钉群（二维码附后）。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（二）集中培训学校拟于赛前举办竞赛集中培训，由大赛技术支持单位杭州贝腾科技有限公司以现场形式为各团队提供免费的竞赛平台学习训练与培训指导，所有报名参赛团队成员必须参加培训。培训时间地点另行通知。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（三）举办竞赛赛事时间：2024年9月12日系统依据参赛团队软件模拟情况自动统计成绩，赛事结束时给出团队总分及排名。学校通过校赛成绩择优推荐进入省赛。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">奖项设置与奖励</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（一）校赛按参赛团队数的10%、20%、30%设置一、二、三等奖。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（二）奖励：学校鼓励广大符合报名条件的学生积极报名参赛，对参赛获奖的学生，发放证书与纪念品，并按照《贵阳人文科技学院学科竞赛管理办法》的有关规定给予奖励。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">其他</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（一）各学院应积极动员、广泛宣传，认真组织好本学院的报名参赛工作。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">（二）联系方式创业综合模拟、数字营销模拟联系人：金铭：13595042161 &nbsp;财务决策模拟赛项：杨尚钊：15885102951技术支持单位联系人：孔端：18708557142。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;"></span></p><p style="text-align:center"><img src="/uploads/20240612/55a353c9c8e433cf5b36f8a879c17a8a.png" alt="微信图片_20240612163301.png" width="754" height="780" border="0" vspace="0" style="width: 754px; height: 780px;"/></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="color: #A5A5A5; font-family: 宋体, SimSun;">&nbsp;</span><br/></p><p style="line-height: 2em;"><br/></p><p style="line-height: 2em;"><br/></p><p><br style="text-wrap: wrap;"/></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
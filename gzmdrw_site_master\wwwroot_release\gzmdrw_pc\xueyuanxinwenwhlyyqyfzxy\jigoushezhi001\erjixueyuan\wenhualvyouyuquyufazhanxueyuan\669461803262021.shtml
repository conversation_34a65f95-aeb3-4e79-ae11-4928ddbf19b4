<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学术讲座|做一个有“根”的民宿</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/669461803262021.shtml" class="text-decoration-none text-dark fw-bold">
                                                学术讲座|做一个有“根”的民宿
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-04-07</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="line-height: 2em; text-align: center;"><strong><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">做一个有“根”的民宿</span></strong></p><p style="line-height: 2em; text-align: center;"><strong><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">中国传统山水精神在现代民宿中的应用</span></strong></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">4月3日，我院有幸邀请到国内知名民宿品牌“匠庐”创始人王兵，在大学城校区图书馆报告厅做了题为《做一个有“根”的民宿——中国传统山水精神在现代民宿中的应用》的学术讲座，为师生带来一场关于文化传承与产业创新的深度分享。学院全体教职工、民宿经营管理微专业及2022级旅游管理、文化产业管理、非物质文化遗产保护专业学生参加本次讲座。</span></p><p><img src="/resources/image/2025/04/25/669462790058053.jpg" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">在欢迎致辞中，金院长向参会师生介绍了匠庐品牌在国内民宿行业中的地位与贡献，并向王兵先生介绍了我院“民宿经营管理”微专业建设情况。开讲前，学院向王兵先生颁发了客座教授证书，希望双方能在校企合作方面更加深入。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">讲座中，王兵先生围绕中国传统文化中的“山水之乐”展开分享，从自然山水、人文山水、性情山水三个层次逐层递进，阐述了如何在民宿选址、民宿美学空间营造、民宿生活美学气质塑造方面运用中国传统山水精神。王兵以匠庐品牌在贵州打造的“匠庐·村晓”“匠庐·雅路古”等标杆项目为例，生动阐释了“大自然才是主角，建筑仅是用来填空”的设计理念。他还提到，民宿的选址应“隐世而不避世”，民宿的设计要注重“以商业需求为导向的在地文化表达”，保留建筑的乡土特质。</span></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">在随后的互动环节，王兵先生还进一步与师生交流了自己对“地方文化在民宿中的取舍”“民宿管家需要哪些特质”等问题的看法，并现场进行品牌宣讲，热情邀请同学们以观摩学习、假期学习、实习就业等各种方式进一步了解匠庐品牌、切身体悟民宿产业。</span></p><p><img src="/resources/image/2025/04/25/669462869008453.jpg" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="line-height: 2em; text-align: justify; text-indent: 2em;"><span style="font-size: 16px; font-family: 宋体, SimSun; color: #7F7F7F;">本次学术讲座，让学院师生触及行业龙头的经营理念，为未来学院专业建设、课程建设及人才培养提供了行业素材。</span></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学院举办《艺术视界》学生作品展</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000006202.shtml" class="text-decoration-none text-dark fw-bold">
                                                学院举办《艺术视界》学生作品展
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-11-27</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>225</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent:37px;line-height:150%"><span style=";font-family:宋体;line-height:150%;font-size:19px">11月26日</span><span style=";font-family:宋体;line-height:150%;font-size:19px">学院</span><span style=";font-family:宋体;line-height:150%;font-size:19px">《艺术视界》</span><span style=";font-family:宋体;line-height:150%;font-size:19px">学生作品展</span><span style=";font-family:宋体;line-height:150%;font-size:19px"><span style="font-family:宋体">在师德楼</span><span style="font-family:宋体">2楼隆重开幕。本次展览</span></span><span style=";font-family:宋体;line-height:150%;font-size:19px">作品涵盖</span><span style=";font-family:宋体;line-height:150%;font-size:19px">动画、视觉传达设计、环境设计三个专业的</span><span style=";font-family:宋体;line-height:150%;font-size:19px">学生作品</span><span style=";font-family:宋体;line-height:150%;font-size:19px">。</span></p><p style="text-indent:37px;line-height:150%"><span style=";font-family:宋体;line-height:150%;font-size:19px">作品展现场，动画专业设计作品涵盖传统手绘动画到现代数字动画多种风格，展现了动画专业学生对动画叙事和动画技术的深刻理解；</span><span style=";font-family:宋体;line-height:150%;font-size:19px">视觉传达设计专业的学生</span><span style=";font-family:宋体;line-height:150%;font-size:19px">作品</span><span style=";font-family:宋体;line-height:150%;font-size:19px">则通过</span><span style=";font-family:宋体;line-height:150%;font-size:19px">包装、</span><span style=";font-family:宋体;line-height:150%;font-size:19px">海报、插画、品牌设计等多种形式，传达了他们对社会现象的敏锐洞察和创意表达</span><span style=";font-family:宋体;line-height:150%;font-size:19px">；</span><span style=";font-family:宋体;line-height:150%;font-size:19px">环境设计专业的学生作品则聚焦于空间与环境的和谐共生，通过模型、效果图和设计方案，展示了对未来居住和公共空间的创新构想。</span></p><p style="text-indent:37px;line-height:150%"><span style=";font-family:宋体;line-height:150%;font-size:19px">本次</span><span style=";font-family:宋体;line-height:150%;font-size:19px">《艺术视界》</span><span style=";font-family:宋体;line-height:150%;font-size:19px">学生作品展不仅为</span><span style=";font-family:宋体;line-height:150%;font-size:19px">设计展不仅</span><span style=";font-family:宋体;line-height:150%;font-size:19px">为学生</span><span style=";font-family:宋体;line-height:150%;font-size:19px">提供了一个</span><span style=";font-family:宋体;line-height:150%;font-size:19px">展示</span><span style=";font-family:宋体;line-height:150%;font-size:19px">创意与才华的机会</span><span style=";font-family:宋体;line-height:150%;font-size:19px">，</span><span style=";font-family:宋体;line-height:150%;font-size:19px">更是一次</span><span style=";font-family:宋体;line-height:150%;font-size:19px">设计思维</span><span style=";font-family:宋体;line-height:150%;font-size:19px">的碰撞和灵感的交流</span><span style=";font-family:宋体;line-height:150%;font-size:19px">，</span><span style=";font-family:宋体;line-height:150%;font-size:19px">更是一次专业实践成果的展示和检验</span><span style=";font-family:宋体;line-height:150%;font-size:19px">。</span></p><p style="text-align:center"><img src="/uploads/20241210/740bfa0fe6c1b9ca87758d1dbc7274d9.jpg" alt="图片1(1).jpg"/><span style=";font-family:宋体;font-size:14px">&nbsp;</span></p><p style="text-align:center"><strong><span style="font-family: 宋体;font-size: 19px">展览开幕式</span></strong></p><p style="text-align:center"><img src="/uploads/20241210/e1219b4f264870f0ca211d6a9aa26959.jpg" alt="图片2(1).jpg"/><strong><span style="font-family: Calibri;color: #C00000;font-size: 19px">&nbsp;</span></strong></p><p style="text-align:center"><strong><span style="font-family: 宋体;font-size: 19px">展览现场</span></strong></p><p style="text-align:center"><img src="/uploads/20241210/628fbb674c4df78e86b1b6f6c563ee4e.jpg" alt="图片3(1).jpg"/><strong><span style="font-family: Calibri;color: #C00000;font-size: 19px">&nbsp;</span></strong></p><p style="text-align:center"><strong><span style="font-family: 宋体;font-size: 19px">展览现场</span></strong></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
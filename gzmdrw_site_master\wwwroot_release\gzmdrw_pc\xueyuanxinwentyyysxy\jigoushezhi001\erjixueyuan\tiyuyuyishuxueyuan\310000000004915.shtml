<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学院2024届视觉传达设计、环境设计毕业设计作品展顺利举行</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000004915.shtml" class="text-decoration-none text-dark fw-bold">
                                                学院2024届视觉传达设计、环境设计毕业设计作品展顺利举行
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-06-02</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1142</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><section style="box-sizing: border-box; padding: 0px 10px; position: relative; font-family: &quot;Open Sans&quot;, sans-serif; font-size: 14px; text-wrap: wrap; background-color: rgb(255, 255, 255); margin: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9);"><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 2.2667em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;">在初夏的五月，体育与艺术学院迎来了一年一度的视觉盛宴——2024届视觉传达设计与环境设计专业的毕业设计作品展。本次展览集中展示了毕业生们四年来的学习成果和创作才华。作为学院教学成果的重要展示平台，本次毕业设计作品展旨在鼓励毕业生们充分发挥自己的创意和想象力，将所学的专业知识运用到实际设计中，为未来的职业生涯奠定坚实的基础。</p></section><p></p><section style="box-sizing: border-box; padding: 0px; position: relative; font-family: &quot;Open Sans&quot;, sans-serif; font-size: 14px; text-wrap: wrap; background-color: rgb(255, 255, 255); margin: 10px 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); text-align: center;"><section style="box-sizing: border-box !important; display: inline-block; padding: 0px; position: relative; margin: 0px; outline: 0px; max-width: 100%; vertical-align: middle; line-height: 0; overflow-wrap: break-word !important;"><img src="/uploads/20240603/d612ff9e543f33bdd573d61da1691126.jpg" alt="3.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></section></section><section style="box-sizing: border-box; padding: 0px 10px; position: relative; font-family: &quot;Open Sans&quot;, sans-serif; font-size: 14px; text-wrap: wrap; background-color: rgb(255, 255, 255); margin: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9);"><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><span style="box-sizing: border-box !important; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;"><span style="box-sizing: border-box !important; font-weight: bolder; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; overflow-wrap: break-word !important;">设计展开幕现场</span></span></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 2.2667em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><span style="box-sizing: border-box !important; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; text-indent: 0em; overflow-wrap: break-word !important;">贵阳人文科技学院教务处处长李纯虎，体育与艺术学院院长雷帮齐教授，教务处副处长马跃，体育艺术学院美术学教研室副主任严文同，以及环境设计、视觉传达的教师代表纷纷来到现场，共同欣赏毕业生们的作品。教务处处长李纯虎在开幕式上致辞，对毕业生们的努力和成果表示肯定，并鼓励他们在未来的设计道路上继续追求卓越。</span></p></section><section style="box-sizing: border-box; padding: 0px; position: relative; font-family: &quot;Open Sans&quot;, sans-serif; font-size: 14px; text-wrap: wrap; background-color: rgb(255, 255, 255); margin: 10px 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); text-align: center;"><section style="box-sizing: border-box !important; display: inline-block; padding: 0px; position: relative; margin: 0px; outline: 0px; max-width: 100%; vertical-align: middle; line-height: 0; overflow-wrap: break-word !important;"><img src="/uploads/20240603/8b375da43879f7f8cb2b4792dbe6c8e3.jpg" alt="4.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></section></section><section style="box-sizing: border-box; padding: 0px 10px; position: relative; font-family: &quot;Open Sans&quot;, sans-serif; font-size: 14px; text-wrap: wrap; background-color: rgb(255, 255, 255); margin: 0px; outline: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9);"><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 2.2667em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;">&nbsp;展览现场汇聚了众多精彩作品，涵盖了视觉传达设计和环境设计的多个方向。视觉传达设计作品包括品牌形象设计、包装设计、海报设计等，他们通过巧妙的构思和独特的视角，将文字、图形和色彩融为一体，呈现出丰富多彩的视觉效果。环境设计作品则涵盖了室内设计、景观设计等领域，他们通过对空间、材质和光影的巧妙运用，打造出一个又一个充满创意和想象力的空间环境。无论是充满现代感的商业空间，还是温馨舒适的家居环境，都让人眼前一亮，赞叹不已。这些作品不仅展示了毕业生们对空间设计的深刻理解，也体现了他们对未来生活方式的探索和追求。</p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/7344a1830a1c2a03984f5ce94aec626d.jpg" alt="5.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 1rem; text-rendering: optimizelegibility; line-height: 30px; text-align: center; -webkit-font-smoothing: antialiased !important;"><img src="/uploads/20240603/9724306304b49f0767e8b52cc48b7bff.jpg" alt="6.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/8b1171224648175e528e468dff380960.jpg" alt="7.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/33faa09ef8e7503630e09fb65eab96ba.jpg" alt="8.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/d33bf896faee9dfb04c635c1fab343e7.jpg" alt="9.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/><img src="/uploads/20240603/694aed95caea96f3145c9f8d308e75cf.jpg" alt="10.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/e2010d1dc47a6a88d4ea32553af18604.jpg" alt="11.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 2.2667em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><br style="box-sizing: border-box;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; text-indent: 0em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/b3730bee8b357726b17e670ef71366cb.jpg" alt="12.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 0em; text-align: center; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/07431098963a2d173da467bcf7cd8669.jpg" alt="13.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; text-indent: 0em; box-sizing: border-box !important; -webkit-font-smoothing: antialiased !important; overflow-wrap: break-word !important;"><img src="/uploads/20240603/a4fc9a4b104411309c146c3b0c8cc285.jpg" alt="14.jpg" style="box-sizing: border-box; vertical-align: middle; border-style: none; max-width: 100%; height: auto;"/></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-align: center; -webkit-font-smoothing: antialiased !important;"><span style="box-sizing: border-box !important; font-weight: bolder; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; overflow-wrap: break-word !important;"><span style="box-sizing: border-box !important; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 12px; color: #A0A0A0; overflow-wrap: break-word !important;">部分毕业设计作品</span></span></p><p style="box-sizing: border-box; margin-top: 0px; margin-bottom: 0px; text-rendering: optimizelegibility; line-height: 30px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; text-indent: 2em; -webkit-font-smoothing: antialiased !important;"><span style="box-sizing: border-box !important; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; text-indent: 2.2667em; overflow-wrap: break-word !important;">此次毕业设计作品展不仅是毕业生们四年学习成果的展示，也是学院教学质量的一次检验。通过展览，学院为学生们提供了一个展示自我的平台，同时也促进了学生之间的交流与学习。此外，愿各位毕业生们前程似锦，未来可期，带着智慧和勇气，开启人生的新篇章！</span></p></section><p></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_“第八届中国大学生人力资源创新实践大赛”经验分享会活动顺利举办</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000003584.shtml" class="text-decoration-none text-dark fw-bold">
                                                “第八届中国大学生人力资源创新实践大赛”经验分享会活动顺利举办
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-12-03</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2839</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p><span style=";font-family:仿宋-GB2312;color:#333333;font-size:19px">&nbsp; &nbsp; &nbsp; </span><span style="color: #333333; font-size: 19px; font-family: 宋体, SimSun;">&nbsp;为持续提高我校学生的创新实践能力，助力其增强求职本领，促进高质量就业，11月30日，我院在学校大学城校区举办了“第八届中国大学生人力资源创新实践大赛”经验分享会活动。经济与管理学院党总支书记朱克超、副院长陈福娣、人力资源管理教研室主任张泽雄出席分享会，经济与管理学院人力资源管理教研室全体教师、人力资源管理专业学生以及参赛选手参加，分享会由人力资源管理教研室老师文诗逸主持。</span></p><p><span style="color: #333333; font-size: 19px; font-family: 宋体, SimSun;"><img src="/uploads/20231203/cb26963f06b36c696e970ba5f7d5aacd.png" alt="图片1.png"/></span></p><p style="text-align:center"><span style="font-family: 宋体, SimSun;"><strong><span style="font-size: 16px; font-family: 宋体, SimSun;">活动现场</span></strong><strong><span style="font-size: 16px; font-family: 宋体, SimSun;">&nbsp;</span></strong></span></p><p style="text-indent:37px;text-align:justify;text-justify:inter-ideograph;line-height:33px"><span style="color: #333333; font-size: 19px; font-family: 宋体, SimSun;">分享会上，朱克超书记表示学科竞赛是一个学习和交流的平台，参加比赛不仅能够提高自身专业知识水平和能力，更重要的是在比赛这个过程中更好地认识自己并开拓视野。他强调，学生们应积极参加各类学科竞赛，为自己未来的职业发展打下坚实的基础。</span></p><p style="text-align:center;line-height:33px"><img src="/uploads/20231203/8a969902db04c1eb84167af5e475e6bb.png" alt="图片2.png"/></p><p style="text-align: center;"><span style="font-family: 宋体, SimSun;"><strong><span style="font-size: 16px; font-family: 宋体, SimSun;">经济与管理学院朱克超书记发言</span></strong><strong><span style="font-size: 16px; font-family: 宋体, SimSun;">&nbsp;</span></strong></span></p><p style="text-indent:37px;text-align:justify;text-justify:inter-ideograph"><span style="color: #333333; font-size: 19px; font-family: 宋体, SimSun;">随后，各参赛选手依对于这次比赛的感想进行分享。她们纷纷表示无论是在赛前，赛中还是赛后，每一次都收获了不同的体验与成长。此外，她们不仅在比赛过程中提高了自己的能力和素质，还收获了宝贵的经验和友谊。</span></p><p style="text-indent:37px"><span style="color: #333333; font-size: 19px; font-family: 宋体, SimSun;">最后，参加分享会的学生表示听了参赛者的分享后对以后参加竞赛产生了浓厚的兴趣，也希望通过比赛能够拓宽自己的视野和知识面，从而为自己的成长和发展注入新的动力。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
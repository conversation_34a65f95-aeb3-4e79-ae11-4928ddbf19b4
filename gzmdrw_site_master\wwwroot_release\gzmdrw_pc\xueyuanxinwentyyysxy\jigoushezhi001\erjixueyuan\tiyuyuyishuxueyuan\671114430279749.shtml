<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学院召开中国国际大学生创新大赛（2025）宣讲动员会</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/671114430279749.shtml" class="text-decoration-none text-dark fw-bold">
                                                学院召开中国国际大学生创新大赛（2025）宣讲动员会
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-04-25</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px"><span style="font-family:宋体">为加强学院创新创业人才培养，进一步点燃青年学生的创新创造热情，做好中国国际大学生创新大赛（</span><span style="font-family:宋体">2025）相关筹备工作，学院分别召开指导老师和参赛学生动员宣讲会。</span></span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px">4月24日，学院针对指导老师在师德楼305举办了“体育与艺术学院中国国际大学生创新大赛（2025）宣讲动员会”。本次会议邀请了体育与艺术学院创新创业学院李笑然老师做分享，体育与艺术学院全体老师参与。4月23日，学院针对参赛学生在师德楼305举办了“体育与艺术学院中国国际大学生创新大赛（2025）宣讲动员会”。本次活动邀请学校创新创业学院教师严泽宇进行宣讲，学院团委书记张鸿雁老师主持宣讲会，学院各班学生代表100余人聆听宣讲。</span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px"></span></p><p style="text-align: center;"><img src="/resources/image/2025/04/30/671115196133445.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="margin-top: 0px; margin-right: 0px; padding: 0px; text-align: center; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em;"><span style="font-family: 宋体; letter-spacing: 0px; font-size: 14px;">宣讲动员会现场</span></p><p style="text-align: center;"><img src="/resources/image/2025/04/30/671114971156549.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="margin-top: 0px; margin-right: 0px; padding: 0px; text-align: center; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em;"><span style="font-family: 宋体; letter-spacing: 0px; font-size: 14px;">宣讲动员会现场</span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px">宣讲会上，李笑然老师和严泽宇老师凭借丰富的实践经验，深入浅出地讲解了大赛的背景、规则、评审标准以及参赛技巧，通过政策解读、赛事介绍、案例分享等方面的解读，为学生全面深入地剖析大赛内容，帮助学生全方位了解大赛、筹备大赛。</span><span style="font-family: 宋体; font-size: 19px; letter-spacing: 0px; text-align: center;">&nbsp;</span></p><p style="text-align: center;"><img src="/resources/image/2025/04/30/671115061510213.png" class="art-body-img" style="max-width: 100%; height: auto;" /></p><p style="margin-top: 0px; margin-right: 0px; padding: 0px; text-align: center; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em;"><span style="font-family: 宋体; letter-spacing: 0px; font-size: 14px;">学校创新创业学院严泽宇老师进行宣讲</span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px">学院党委书记张健在宣讲尾声做动员讲话，鼓励各位老师积极参与，抓工作、抓部署，抓创新，带领学生参与各项创新创业比赛。</span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px">张鸿雁老师也鼓励全体同学积极思考、勇于探索、敢于创新，积极参加到比赛中，以大赛为契机，将比赛作为一个难得的实践平台，在比赛中不断锻炼和提升自己的综合实力。</span></p><p style="margin-top: 0px; margin-right: 0px; margin-left: 0px; text-indent: 37px; padding: 0px; vertical-align: baseline; background: rgb(255, 255, 255); line-height: 2em; text-align: justify;"><span style="font-family: 宋体;letter-spacing: 0;font-size: 19px">一直以来，学院紧跟学校创新创业工作步伐，积极部署、认真落实，将创新创业工作作为学院重点开展的工作项目之一，通过积极组织动员师生参加各类创新创业赛事、组织参与相关讲座等方式，持续激发学院师生的创新创造热情，为培养更多创新人才贡献智慧与力量。未来，学院将进一步营造创新创业的良好氛围，引导学生树立创新意识，提升实践能力，为推动国家创新发展和学校教育高质量发展注入新的活力。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
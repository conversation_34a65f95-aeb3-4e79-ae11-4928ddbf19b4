<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_快来看看别人家的宿舍吧~</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/wenhualvyouyuquyufazhanxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">文化旅游与区域发展学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">文化旅游与区域发展学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/rencaipeiyangwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">人才培养</a>
                                <a href="/shiziduiwuwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/jiaoxuekeyanwhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">教学科研</a>
                                <a href="/xueshenggongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">学生工作</a>
                                <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tongzhigonggaowhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                                <a href="/dangtuangongzuowhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">党团工作</a>
                                <a href="/zhidujianshewhlyyqyfzxxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/" class="list-group-item list-group-item-action ">制度建设</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenwhlyyqyfzxy/jigoushezhi001/erjixueyuan/wenhualvyouyuquyufazhanxueyuan/310000000003750.shtml" class="text-decoration-none text-dark fw-bold">
                                                快来看看别人家的宿舍吧~
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">文化旅游与区域发展学院宣传部</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2023-12-19</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>3047</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 2em; text-align: justify; line-height: 2em;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 24px; text-align: justify; text-indent: 24px; text-wrap: wrap; widows: 1;">在校园中，寝室是同学们生活学习的主要空间，我们与来自天南地北的室友们结识，每天打打闹闹相互鼓励，开心每一天。为丰富同学们的大学生活，打造干净整洁的寝室环境，营造互助互爱、健康、积极向上的寝室文化氛围，发挥专业特色，弘扬人文创新精神。我院面向全体学子于11月30日至12月初分别在大学城校区、花溪校区组织开展“树学风，爱我寝”的文明寝室评选活动。</span></p><p style="text-indent: 2em; text-align: justify; line-height: 2em;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 24px; text-align: justify; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FEF2E8;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;">此次活动主要从宿舍卫生、学风建设、宿舍安全等几个方面进行评比，采用评委现场打分和参观的方式进行评比，抽取每班一名成员与两名学生会评审员一起去现场进行感受与打分。</span></span></p><p><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 24px; text-align: justify; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FEF2E8;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"></span></span></p><p style="text-align:center"><img src="/uploads/20231221/d1b7ee830df41e62de2a515940bd9005.png"/></p><p style="text-align:center"><img src="/uploads/20231221/bfb89c5a03232bd43de775307614b23e.png"/></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(109, 87, 80); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; text-wrap: wrap; widows: 1; text-indent: 37px; text-align: justify; line-height: 2em; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;">在评比过程中，各个参评寝室地面、桌面干净整洁、物品堆放整齐、用电安全，赢得了各评委的一致赞赏。</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(109, 87, 80); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; text-wrap: wrap; widows: 1; text-indent: 37px; text-align: justify; line-height: 2em; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;">各评委本着公平、公开、公正的原则经过现场评分，花溪校区共有3个寝室获得文明寝室称号，大学城校区共有6个寝室获得文明寝室称号，具体获奖名单如下：</span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(109, 87, 80); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; text-wrap: wrap; widows: 1; text-indent: 37px; text-align: justify; line-height: 2em; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;">花溪校区</span><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; color: #262626; font-family: 仿宋;">：</span><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-family: 仿宋;">1-630、1-635、1-660</span></span></p><p style="margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; clear: both; min-height: 1em; color: rgb(109, 87, 80); font-family: system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;PingFang SC&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 17px; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; text-wrap: wrap; widows: 1; text-indent: 37px; text-align: justify; line-height: 2em; background-color: rgb(255, 255, 255); box-sizing: border-box !important; overflow-wrap: break-word !important;"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;">大学城校区：2-114、2-118、2-128、2-202、5-311、5-422</span></p><p style="text-align:center"><span style="margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; word-wrap: break-word !important; line-height: 2em; font-size: 16px; font-family: 宋体; color: #888888;"><img src="/uploads/20231221/793b38d7dfc593e47cd7bec7dd693917.jpg" alt="5.jpg"/></span></p><p style="text-indent: 2em; text-align: justify; line-height: 2em;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 24px; text-align: justify; text-indent: 24px; text-wrap: wrap; widows: 1; background-color: #FEF2E8;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1.5px; line-height: 32px; text-align: justify; text-indent: 34px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;"><span style="color: #888888; font-family: 宋体; font-variant-numeric: normal; font-variant-east-asian: normal; font-variant-alternates: normal; font-variant-position: normal; letter-spacing: 1px; line-height: 24px; text-align: justify; text-indent: 37px; text-wrap: wrap; widows: 1; background-color: #FFFFFF;">良好的宿舍文明氛围是学生学习、生活的重要保障。文明宿舍评比活动的开展，不仅展现了文旅学子的良好精神面貌和高雅生活情趣，更促进了同学们养成良好的生活习惯，增进了同学们的团结互助精神和宿舍的集体凝聚力，对深化宿舍安全、卫生观念，促进创建美丽校园具有推动作用。</span></span></span><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_喜报|我院行政管理专业学生参加第三届“寻是杯”全国大学生公共管理决策模拟大赛荣获佳绩</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000004398.shtml" class="text-decoration-none text-dark fw-bold">
                                                喜报|我院行政管理专业学生参加第三届“寻是杯”全国大学生公共管理决策模拟大赛荣获佳绩
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-04-30</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1450</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 33px; line-height: 1.5em;"><span style="letter-spacing: 0px; background: #FFFFFF; font-family: 宋体; font-size: 20px;">2024年4月27日至28日，由中国科学学与科技政策研究会主办，中国科学学与科技政策研究会公共管理专业委员会协办，西南交通大学公共管理学院承办，上海哲寻信息科技有限公司提供技术支持的第三届“寻是杯”全国大学生公共管理决策模拟大赛（西南赛区）在西南交通大学成功举行。我院行政管理专业学生尚丹妮、周容琴、张林组成的“对对队”参加了比赛，并获得佳绩。</span></p><p style="text-align: center; line-height: 1.5em;"><span style="font-family: "><img src="/uploads/20240430/ebd4c47bfdd00d303bd5c9067f0aebb2.png" alt="图片1.png"/>&nbsp;</span><span style="font-family: 宋体; letter-spacing: 0px; font-size: 20px;">图1 我院学生参赛队伍</span></p><p style="text-indent: 33px; line-height: 1.5em;"><span style="font-family: 宋体; letter-spacing: 0px; background: #FFFFFF; font-size: 20px;">4月27日上午9时，举行大赛开幕式。西南交通大学党委副书记张学龙、中国科学学与科技政策研究会常务理事刘兰剑、华南理工大学公共管理学院院长李胜会、湘潭大学公共管理学院院长梁丽芝、西南交通大学公共管理学院党委书记高凡、西南交通大学公共管理学院院长王永杰、上海哲寻信息科技有限公司副总经理徐坤以及来自43所高校的44支参赛队伍的260多位参赛师生出席大赛开幕式。</span></p><p style="text-align: center; line-height: 1.5em;"><span style="font-family: "><img src="/uploads/20240430/9dea5e120233c0078cf43aad5baafcc7.png" alt="图片2.png"/>&nbsp;</span><span style="font-family: 宋体; letter-spacing: 0px; font-size: 20px;">图2 参赛师生合影</span></p><p style="text-indent: 33px; line-height: 1.5em;"><span style="letter-spacing: 0px; background: #FFFFFF; font-family: 宋体; font-size: 20px;">开幕式结束后，44支参赛团队进行了公共管理决策演练沙盘对抗。各参赛队伍在严谨的赛场秩序之下，表现出了令人赞赏的沉着与冷静。同学们不仅展现了出色的团结协作精神，而且在处理复杂问题时，他们能够深入分析，细致入微，提出了既符合客观实际又具备可行性的解决方案。这一过程，无一不充分体现了参赛高校学子们扎实的理论功底和全面均衡的综合素质，他们的表现为我们展示了当代大学生的学术风范和专业素养。经过6个小时的公共管理决策演练沙盘对抗竞赛，最终我院参赛队伍取得了二等奖的好成绩。</span></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20240430/079df69a2872acd97d8fec23ed52c04d.png" alt="图片3.png"/></p><p style="text-align: center; line-height: 1.5em;"><img src="/uploads/20240430/00f1126b0c6dcea02e0488908a3633a8.png" alt="图片4.png"/></p><p style="text-align: center; line-height: 1.5em;"><span style="font-family: 宋体; letter-spacing: 0px; background: #FFFFFF; font-size: 20px;">图片3 学生获奖</span></p><p style="line-height: 1.5em;"><span style="letter-spacing: 0px; background: #FFFFFF; font-family: 宋体; font-size: 20px;">&nbsp; &nbsp;本次大赛通过运用数理模型和数字技术构建案例情景，不仅深化了科技与学科知识的结合，而且增强了学生的系统思维、实际应用能力和团队协作能力，进一步提升了我院人才培养质量，体现了我院对全国教育大会精神和新时代本科教育工作会议精神的积极响应，旨在实现立德树人的核心任务，并推动新文科建设和数智化转型进程。</span></p><p style="line-height: 1.5em;"><span style="font-family: 宋体; letter-spacing: 0px; background: #FFFFFF; font-size: 20px;"><br/></span></p><p style="line-height: 1.5em;"><span style="font-family: 宋体; letter-spacing: 0px; background: #FFFFFF; font-size: 20px;"></span></p><p style="text-align: right; line-height: 1.5em;"><br/></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
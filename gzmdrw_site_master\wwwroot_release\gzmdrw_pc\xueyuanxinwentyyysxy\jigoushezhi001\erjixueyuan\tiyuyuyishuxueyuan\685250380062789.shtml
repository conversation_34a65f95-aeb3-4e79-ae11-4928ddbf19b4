<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_国画里的东方诗意 | 2025届美术学专业毕业生国画作品展</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/tongzhigonggaotyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">通知公告</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/685250380062789.shtml" class="text-decoration-none text-dark fw-bold">
                                                国画里的东方诗意 | 2025届美术学专业毕业生国画作品展
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2025-06-02</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>0</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <section style="-webkit-tap-highlight-color: transparent; margin: -10px 0px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); visibility: visible; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px 10px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; letter-spacing: 2px; line-height: 2; visibility: visible;"><p style="-webkit-tap-highlight-color: transparent; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: justify; visibility: visible; margin-top: 5px; text-indent: 2em; overflow-wrap: break-word !important;"><span style="font-family: 宋体, SimSun; font-size: 20px; text-indent: 2.25em;">时维乙巳初夏，</span><strong style="font-family: 宋体, SimSun; font-size: 20px; text-indent: 2.25em; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; visibility: visible; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; visibility: visible; box-sizing: border-box !important; overflow-wrap: break-word !important;">2025届美术学专业毕业生</span></strong><span style="font-family: 宋体, SimSun; font-size: 20px; text-indent: 2.25em;">以丹青为媒，集四年学业之粹，汇成此青春画卷。笔墨纵横处，见少年意气，藏天地乾坤。国画之韵、油画之彩，皆承千年文脉而发新声。此展既呈山川之壮阔，复现人文之精微。或见苗岭晨曦染绢素，或观侗寨炊烟入油彩。起承转合间，有传统法度，虚实浓淡处，见时代精神。四载寒窗凝于此展，千般匠心俱在方寸。非独技艺之展示，实为心灵之独白。</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: justify; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="text-align: center; text-indent: 2.25em; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; visibility: visible; font-family: 宋体, SimSun; font-size: 20px; overflow-wrap: break-word !important;">此次展览共展出毕业创作58幅（组），其中国画30幅（组）；油画28幅（组）。</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: justify; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><img src="/resources/image/2025/06/09/685253946748997.png" alt="图片" style="text-align: center; font-family: 宋体, SimSun; font-size: 20px; text-indent: 2.25em; height: auto; max-width: 100%;" /></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="font-size: 14px; font-family: 宋体, SimSun;"><strong style="text-indent: 2.25em; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(160, 160, 160); font-size: 12px; letter-spacing: 0.544px; overflow-wrap: break-word !important;"><span leaf="" style="font-size: 14px; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">体育与艺术学院美术学专业2025届毕业生作品展暨春季写生展</span></strong></span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><strong style="font-size: 20px; letter-spacing: 0.544px; text-indent: 2.25em; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><br/></strong></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><strong style="font-size: 20px; letter-spacing: 0.544px; text-indent: 2.25em; -webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">美术学专业2025届毕业生国画作品展</strong><span style="font-family: 宋体, SimSun; font-size: 20px; text-align: justify; text-indent: 2.25em;"></span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: justify; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; visibility: visible;"></span></p><section style="-webkit-tap-highlight-color: transparent; margin: 20px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; justify-content: center; display: flex; flex-flow: row; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px 5px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: middle; width: auto; min-width: 5%; flex: 0 0 auto; height: auto; align-self: center; border-style: solid; border-width: 0px;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: left; justify-content: flex-start; display: flex; flex-flow: row;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: middle; width: auto; min-width: 5%; flex: 0 0 auto; height: auto; align-self: center;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: justify; font-size: 12px; color: rgb(203, 202, 75);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; font-size: 20px; box-sizing: border-box !important; overflow-wrap: break-word !important;">代表作品</span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px 0px 0px 5px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: middle; width: auto; align-self: center; flex: 100 100 0%; height: auto;"><section style="-webkit-tap-highlight-color: transparent; margin: 0.5em 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; background-color: rgb(203, 202, 75); height: 1px;"><svg viewbox="0 0 1 1" style="float:left;line-height:0;width:0;vertical-align:top;"></svg></section></section></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; vertical-align: middle; width: auto; min-width: 5%; flex: 0 0 auto; height: auto; align-self: center;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; line-height: 0;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 19px; height: auto;"><img class="rich_pages wxw-img" data-ratio="2.3699059561128526" data-s="300,640" data-w="319" width="100%" data-src="iurl://resources/image/2025/06/09/685254156587077.png?type=resource&amp;id=685254156587077&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="6" src="/resources/image/2025/06/09/685254164041797.png" _width="100%" alt="图片" data-report-img-idx="4" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 19px !important; visibility: visible !important; max-width: 100%;" /></section></section></section></section><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.8851851851851852" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254156656709.png?type=resource&amp;id=685254156656709&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="7" src="/resources/image/2025/06/09/685254164099141.png" _width="100%" alt="图片" data-report-img-idx="6" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">杨建国 《赤壁墨色》</strong></span></p></section></section><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.2546296296296295" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254156726341.png?type=resource&amp;id=685254156726341&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="8" src="/resources/image/2025/06/09/685254164172869.png" _width="100%" alt="图片" data-report-img-idx="7" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">张锐 《仡山人家》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8305555555555555" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254156795973.jpeg?type=resource&amp;id=685254156795973&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="9" src="/resources/image/2025/06/09/685254164242501.jpeg" _width="100%" alt="图片" data-report-img-idx="8" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">付盈银 《乌江春韵》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.875925925925926" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254156898373.jpeg?type=resource&amp;id=685254156898373&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="10" src="/resources/image/2025/06/09/685254164348997.jpeg" _width="100%" alt="图片" data-report-img-idx="9" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">冯莹 《 鹰山春景图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.9703703703703703" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254156972101.jpeg?type=resource&amp;id=685254156972101&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="11" src="/resources/image/2025/06/09/685254164504645.jpeg" _width="100%" alt="图片" data-report-img-idx="10" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">邱娅 《梵净山松风图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8777777777777778" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157054021.jpeg?type=resource&amp;id=685254157054021&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="12" src="/resources/image/2025/06/09/685254164594757.jpeg" _width="100%" alt="图片" data-report-img-idx="11" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">宣静怡 《乡韵图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8935185185185186" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157135941.jpeg?type=resource&amp;id=685254157135941&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="13" src="/resources/image/2025/06/09/685254164684869.jpeg" _width="100%" alt="图片" data-report-img-idx="12" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">盛祝 《飞瀑鸣泉图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.2638888888888888" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157234245.jpeg?type=resource&amp;id=685254157234245&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="14" src="/resources/image/2025/06/09/685254164770885.jpeg" _width="100%" alt="图片" data-report-img-idx="13" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">吴艳阳 《偏桥云墨》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8981481481481481" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157307973.jpeg?type=resource&amp;id=685254157307973&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="15" src="/resources/image/2025/06/09/685254164840517.jpeg" _width="100%" alt="图片" data-report-img-idx="14" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">皮正兰 《花江大峡谷》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8666666666666667" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157393989.jpeg?type=resource&amp;id=685254157393989&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="16" src="/resources/image/2025/06/09/685254164926533.jpeg" _width="100%" alt="图片" data-report-img-idx="15" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">李宗妍 《峨眉之韵》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="2.02037037037037" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157488197.jpeg?type=resource&amp;id=685254157488197&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="17" src="/resources/image/2025/06/09/685254165016645.jpeg" _width="100%" alt="图片" data-report-img-idx="16" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">徐佳丽 《望黔灵》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.837962962962963" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157561925.jpeg?type=resource&amp;id=685254157561925&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="18" src="/resources/image/2025/06/09/685254165090373.jpeg" _width="100%" alt="图片" data-report-img-idx="17" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">谢佳怡 《万壑图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8583333333333334" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157643845.jpeg?type=resource&amp;id=685254157643845&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="19" src="/resources/image/2025/06/09/685254165168197.jpeg" _width="100%" alt="图片" data-report-img-idx="18" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">代思思 《龙脊秋韵图》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8666666666666667" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157729861.jpeg?type=resource&amp;id=685254157729861&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="20" src="/resources/image/2025/06/09/685254165241925.jpeg" _width="100%" alt="图片" data-report-img-idx="19" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">伍昊天 《山河新装》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.9700854700854702" data-s="300,640" data-w="702" width="100%" data-src="iurl://resources/image/2025/06/09/685254157807685.jpeg?type=resource&amp;id=685254157807685&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="21" src="/resources/image/2025/06/09/685254165319749.jpeg" _width="100%" alt="图片" data-report-img-idx="20" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 20px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">刘鑫涛 《云起岳麓》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 643.141px; height: auto;"><img class="rich_pages wxw-img" data-ratio="1.8657407407407407" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157881413.jpeg?type=resource&amp;id=685254157881413&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="22" src="/resources/image/2025/06/09/685254165389381.jpeg" _width="100%" alt="图片" data-report-img-idx="21" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 643.141px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px 30px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 12px; color: rgb(160, 160, 160);"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span style="font-size: 14px;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">&nbsp;何五 《南江秘境》</strong></span></p></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px -40px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: center; transform: translate3d(6px, 0px, 0px); line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 63px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.6694444444444444" data-s="300,640" data-w="1080" width="100%" data-src="iurl://resources/image/2025/06/09/685254157971525.png?type=resource&amp;id=685254157971525&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="23" src="/resources/image/2025/06/09/685254165471301.png" _width="100%" alt="图片" data-report-img-idx="22" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 63px !important; visibility: visible !important; max-width: 100%;" /></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 10px 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); display: flex; flex-flow: row; text-align: center; justify-content: center; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px 10px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; width: auto; vertical-align: middle; align-self: center; min-width: 10%; flex: 0 0 auto; height: auto; line-height: 0;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; display: inline-block; width: 40px; height: 40px; vertical-align: top; overflow: hidden; background-color: rgb(243, 243, 241); border-width: 0px; border-radius: 110px; border-style: none; border-color: rgb(62, 62, 62);"><svg viewbox="0 0 1 1" style="float:left;line-height:0;width:0;vertical-align:top;"></svg></section></section></section></section><section style="-webkit-tap-highlight-color: transparent; margin: 20px 0px -90px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); text-align: right; transform: translate3d(-1px, 0px, 0px); line-height: 0; overflow-wrap: break-word !important;"><section nodeleaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; vertical-align: middle; display: inline-block; line-height: 0; width: 124px; height: auto;"><img class="rich_pages wxw-img" data-ratio="0.5398936170212766" data-s="300,640" data-w="752" width="100%" data-src="iurl://resources/image/2025/06/09/685254158024773.png?type=resource&amp;id=685254158024773&amp;st=Local&amp;sid=440515705974853" data-original-style="vertical-align: middle;max-width: 100%;width: 100%;box-sizing: border-box;height: auto !important;" data-index="24" src="/resources/image/2025/06/09/685254165524549.png" _width="100%" alt="图片" data-report-img-idx="23" data-fail="0" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; box-sizing: border-box; vertical-align: middle; overflow-wrap: break-word !important; height: auto; width: 124px !important; visibility: visible !important; max-width: 100%;" /></section></section><p><br/></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;"><br style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;"/></span></p><p><br/></p><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; color: rgb(62, 62, 62); font-family: &quot;PingFang SC&quot;, system-ui, -apple-system, BlinkMacSystemFont, &quot;Helvetica Neue&quot;, &quot;Hiragino Sans GB&quot;, &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, Arial, sans-serif; font-size: 16px; letter-spacing: 0.544px; text-align: justify; text-wrap-mode: wrap; background-color: rgb(255, 255, 255); overflow-wrap: break-word !important;"><section style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px 2px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; text-align: center; font-size: 15px; line-height: 2; letter-spacing: 2px;"><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">从临摹到创作，从青涩到从容</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">每一幅画都是时光刻下的勋章</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">四年伏案雕琢，今日携梦登场</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><span leaf="" style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important;">这一纸画卷是终点，更是新的起点</span></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">愿此去笔耕不辍，以热爱为墨</strong></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 2px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; overflow-wrap: break-word !important;"><strong style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important;">在更广阔的天地续写人生新篇</strong></p></section></section><p><br/></p><p style="-webkit-tap-highlight-color: transparent; margin-top: 0px; margin-bottom: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; clear: both; min-height: 1em; text-align: justify; text-indent: 2.25em; visibility: visible; overflow-wrap: break-word !important;"><span style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box; overflow-wrap: break-word !important; font-size: 15px; visibility: visible;"><br style="-webkit-tap-highlight-color: transparent; margin: 0px; padding: 0px; outline: 0px; max-width: 100%; box-sizing: border-box !important; overflow-wrap: break-word !important; visibility: visible;"/></span><br/></p></section></section><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_教育艺术学部2015级新生与团干户外拓展活动圆满结束</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002481.shtml" class="text-decoration-none text-dark fw-bold">
                                                教育艺术学部2015级新生与团干户外拓展活动圆满结束
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">教育与艺术学部·</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2015-11-23</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1967</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><div class="row" align="center" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><h1 style="margin: 0px; padding: 0px; font-size: 20.8px;"><br/></h1></div><div class="row" style="margin: 10px; padding: 0px 0px 15px; border-bottom: 1px dashed rgb(128, 155, 185); font-family: "><div align="center" style="margin: 0px; padding: 0px;"><strong><span style="font-size: 16pt;">教育艺术学部2015级新生与团干户外拓展活动圆满结束</span></strong></div><strong>&nbsp;</strong><div style="margin: 0px; padding: 0px;"><span style="font-size: 14pt;">&nbsp;&nbsp;&nbsp; 为了加强与学部2015级新生之间的交流，加强学生会成员之间的交流，增进彼此之间的了解和认识，使得学部2015级新生能够更快更好地融入大学校园生活，我学部在11月20日上午10点20分举行了一次素质拓展活动。学部2015级100余名新生和新一届学生会成员参加了此次活动。</span></div><div style="margin: 0px; padding: 0px; text-indent: 14pt;"><span style="font-size: 14pt;">&nbsp;</span><span style="font-size: 14pt;">上午10点20分在足球场集合完毕后，本次活动正式拉开帷幕。虽然冬日的早晨有点冷，但从同学们的轻快地脚步和欢快的笑容上能够看出，对此次素质拓展活动他们以期待已久。在主席团的精心安排下，为活动准备的游戏很快就开始了。先是选号、编队、取队名、队歌和口号。<br/></span></div><div style="margin: 0px; padding: 0px; text-align: center; text-indent: 14pt;"><span style="font-size: 14pt;"><img src="/uploads/20231021/1680bd636d446bb07151c500782de5c5.png" alt="image.png"/></span></div><div style="margin: 0px; padding: 0px; text-indent: 10.5pt;"><span style="font-size: 14pt;">&nbsp;&nbsp;&nbsp;</span><span style="font-size: 14pt;">活动中，由主持人主持大家先做热身活动，整片现场传出开心的笑声，为下面的项目垫下了一个好的开始，项目分为很多项，每一项有它不同的做法，严格的游戏规则，和同学们的积极配合，使每项项目达到了真正目的，同学们的团队精神很浓烈，大家都像家人一样的商讨最佳方案，相互帮助，为团队也做了相应的奉献，涌现了“有福同享，有难同当” 的团队精神 ，长达5个小时的活动时间，尽管时间很长，尽管项目多么艰难，没有人中途放弃，大家都充满了团结的力量，手挽手，肩并肩，共同努力的顺利地完成了本次活动。<br/></span></div><div style="margin: 0px; padding: 0px; text-align: center; text-indent: 10.5pt;"><span style="font-size: 14pt;"><img src="/uploads/20231021/da03f58f0780456a1fde9db997e588a3.png" alt="image.png"/></span><span style="font-size: 14pt;"><br/></span></div><div style="margin: 0px; padding: 0px; text-indent: 10.5pt;">&nbsp;&nbsp;&nbsp;&nbsp;<span style="font-size: 14pt;">本次素质拓展活动在学部团总支学生会的精心策划组织下取得了圆满的成功，参加活动的学生大都感触颇深。团队协作，是给他们留下了深刻印象。很多同学还感叹到这就如同人的一生一样，有成功，有坎坷、有收获、</span>&nbsp;<span style="font-size: 14pt;">有失去、有泪水、有欢笑。&nbsp;虽然素质拓展活动只是很短暂的一天，但给同学们留下了深刻的影响。通过本次活动，加强了学部15级新生之间、新生和学生会成员之间的交流和合作，为学部学生工作更好开展打下了良好的基础。</span></div><div style="margin: 0px; padding: 0px; text-align: center;"><span style="font-size: 14pt;"><img src="/uploads/20231021/b2880860cad92f591a4c3c03d4e3394e.png" alt="image.png"/></span></div></div><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_经济与管理学院举办第二届大学生电商直播培训</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/jingjiyuguanlixueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">经济与管理学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">经济与管理学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuangjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/zhuanyeshezhijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/guanliduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/dangjiangongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">党建工作</a>
                                <a href="/tuanxuegongzuojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">团学工作</a>
                                <a href="/shiziduiwujjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/ziliaoxiazaijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                                <a href="/jiuyexinxijjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">就业信息</a>
                                <a href="/xueyuangonggaojjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">学院公告</a>
                                <a href="/jiaoxuehuodongjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwenjjyglxy/jigoushezhi001/erjixueyuan/jingjiyuguanlixueyuan/310000000004733.shtml" class="text-decoration-none text-dark fw-bold">
                                                经济与管理学院举办第二届大学生电商直播培训
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-05-14</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>1221</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 53px; text-align: left;"><span style=";font-family:仿宋;font-size:21px"><span style="font-family:仿宋"></span></span></p><p><span style=";font-family:仿宋;font-size:21px"><span style="font-family:仿宋">&nbsp; &nbsp;为了提升学生电商直播实操能力，</span><span style="font-family:仿宋">5月7日下午，经济与管理学院在师德楼307会议室举行了经济与管理学院第二届大学生电商直播大赛赛前培训。学院邀请贵州极壹电子商务有限公司电子商务运营总监齐金主讲，由电子商务教研室负责人赵琳菊老师主持，各参赛团队参与此次培训。</span></span></p><p style="text-indent: 28px; text-align: left;"><img src="/uploads/20240514/26ca6af81b573da33440673f3db4b1c6.png" alt="图片1_副本.png"/><span style="font-family:Calibri"><span style="font-size: 14px;">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></span><span style="text-align: left; text-indent: 43px; font-family: 仿宋; font-size: 21px;">本次培训，齐金老师围绕直播平台算法规则、直播流量提升技巧、主播能力等多个同学们在直播中关注的内容，同时结合直播比赛的产品</span><span style="text-align: left; text-indent: 43px; font-family: 仿宋; font-size: 21px;">——</span><span style="text-align: left; text-indent: 43px; font-family: 仿宋; font-size: 21px;">口袋板栗，为参赛同学进行了讲授。细致地介绍了直播时的留人技巧、互动话术、产品讲解话术等多个直播方面的知识。</span></p><p style="text-align: left;"><span style=";font-family:&#39;Times New Roman&#39;;font-size:16px"><img src="/uploads/20240514/46c1aac57795d55416b7f518f7312fe7.png" alt="图片2.png"/>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style="font-family: 仿宋; font-size: 21px; text-indent: 13px;">齐老师的讲解采用理论培训和案例实践的方式相结合，重视培训内容的实用性、针对性、也重视实操技能的培养，有效的保障了培训的效果。培训会后齐老师针对同学们的提问一一进行了解答。</span></p><p style="text-align: left;"><span style=";font-family:&#39;Times New Roman&#39;;font-size:16px"><img src="/uploads/20240514/a224a39a8f8c63b018e08c0a96d7cf28.png" alt="图片3.png"/>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;</span><span style="font-family: 仿宋; font-size: 21px;">通过此次培训会，不同专业的同学对电商直播的认识有了进一步提升，也推动了同学们将理论知识应用到具体实践中，相信同学们能</span><span style="font-family: 仿宋; font-size: 21px;">更好地理解</span><span style="font-family: 仿宋; font-size: 21px;">电商直播</span><span style="font-family: 仿宋; font-size: 21px;">知识、强化</span><span style="font-family: 仿宋; font-size: 21px;">电商直播</span><span style="font-family: 仿宋; font-size: 21px;">能力、提升竞争力，为未来的职业发展打下坚实的基础</span><span style="font-family: 仿宋; font-size: 21px;">。</span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
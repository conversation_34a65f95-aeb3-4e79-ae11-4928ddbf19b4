<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_美术系召开2011届毕业生座谈会</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000002567.shtml" class="text-decoration-none text-dark fw-bold">
                                                美术系召开2011届毕业生座谈会
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">新闻中心</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2011-06-12</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>2106</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style=";padding: 0px;line-height: 32px;text-indent: 32px"><span style="line-height: 32px;font-family: 宋体;font-size: 16px">6</span><span style="line-height: 32px;font-family: 宋体;font-size: 16px">月10日</span><span style="line-height: 32px;font-family: 宋体;font-size: 16px">上午10点，美术系分别召开毕业学生干部、优秀大学毕业生座谈会及志愿服务西部计划、三支一扶、基层选调生、特岗计划等面向基层就业毕业生代表座谈会。党总支副书记马帅老师、政治辅导员赵恩培老师及毕业生代表参加座谈。</span></p><p style=";padding: 0px;line-height: 32px;text-indent: 32px"><span style="line-height: 32px;font-family: 宋体;font-size: 16px">马帅副书记在座谈会上代表系上向参加座谈的毕业生代表并通过他们向2011届全体毕业生顺利完成学业、走向社会、服务大众表示祝贺，对代表们在系上和同学之间充分发挥桥梁和纽带作用表示感谢。马帅副书记对毕业生深情寄语，要求全体毕业生秉持学院院训精神——“修身、自信、乐学、笃行”，在自己的工作岗位上发挥专业特长，为基层建设出谋划策，为百姓健康服好务，为民族兴旺贡献力量。他希望同学们能够公正看待世界，客观评价自己，坦诚面对人生，勤劳勇敢，奋发学习，将经历视为自己最珍贵的财富，善待朋友，宽容异己，反思不足，常怀感恩之心，要智慧地做人、智慧地工作，做到困难面前不灰心，不放弃，用正确的方法达到事半功倍的效果。马帅副书记希望毕业生们能够继续关心、支持母校的建设发展，保持与母校、老师和同学的联系，为母校争光，为祖国加油。</span></p><p><span style="line-height: 32px;font-family: 宋体;font-size: 16px"></span></p><p style=";padding: 0px;line-height: 32px;text-indent: 32px"><span style="line-height: 32px;font-size: 16px">赵恩培老师向毕业生取得的优异成绩表示祝贺，希望毕业生们能够以“修身、自信、乐学、笃行”的院训精神作为自己终身做人行事的标尺，用母校和老师交给的知识、方法和本领服务广大老百姓，使自己的人生价值在实践中成长、在实践中提高、在实践中升华。他希望广大毕业生以母校为坚强后盾，与母校分享成功的喜悦和失败的教训，继续关注母校的发展和进步，常回家看看。</span></p><p></p><p style=";padding: 0px;line-height: 32px;text-indent: 32px"><span style="line-height: 32px;font-family: 宋体;font-size: 16px">座谈会上，与会毕业生代表纷纷敞开心扉，畅谈自己的大学学习生活和心得体会，豪情满怀地畅想自己的人生理想和发展规划，表达他们对母校和恩师的一片深情厚谊和不舍离别的心情，同时也为学院和系上各方面工作的进一步完善和改进提出了宝贵的意见和建议。座谈会在轻松欢快的气氛中结束。<br/></span></p><p style=";padding: 0px;text-align: center;line-height: 32px;text-indent: 32px"><img src="/uploads/20231021/a4c83acaf0af322c4db1c33cf0d650b2.png" alt="image.png"/></p><p style="padding: 0px; line-height: 32px; text-indent: 32px; text-align: center;">座谈会现场&nbsp;</p><p><span style="line-height: 32px;font-family: 宋体;font-size: 16px">&nbsp;&nbsp; 据悉，2011届有65名毕业生，其中获得贵州省优秀毕业生2名，校级优秀毕业生3名。毕业生面向基层就业情况良好，其中大学生志愿服务西部计划报名确认录取2人；考取研究生1人；考取特岗教师10人，考取基层选调生1人。</span></p><p><br/></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>
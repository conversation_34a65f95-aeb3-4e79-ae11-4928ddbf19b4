<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link
      href="/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- 添加 Montserrat 字体 -->
    <link href="/css/font_montserrat.css" rel="stylesheet">

    <!-- 添加Font Awesome图标CSS -->
    <link
    rel="stylesheet"
    href="/css/font_awesome_all.min.css"
  />



    <title>贵阳人文科技学院_学院召开第十九届“挑战杯”全国大学生课外学术科技作品竞赛安排部署会</title>
    <style>
        .list-group {
            border: none;
            border-radius: unset;
        }

        /* 左侧栏标题样式 */
        .title-with-line {
            padding-left: 20px;
            background-color: transparent !important;
            border: none;
            font-weight: bold;
            color: #333;
        }

        .title-with-line::before {
            content: '';
            position: absolute;
            left: 0;
            top: 30%;
            width: 3px;
            height: 40%;
            background-color: #AD1E23;
        }

        /* 子栏目样式 */
        .list-group-item {
            border: none;
            padding: 15px 20px;
            background-color: #f5f5f5;
            margin-bottom: 10px;
            border-radius: unset;
            transition: all 0.3s ease;
        }

        .list-group-item-action {
            font-size: 14px;
            color: #333;
        }

        .list-group-item-action:hover,
        .list-group-item-action.active {
            background-color: #AD1E23;
            color: #fff;
            text-decoration: none;
        }

        .list-group-item-action.active:hover {
            background-color: #92191d;
        }

        /* 分页控件样式 */
        .pagination {
            margin-bottom: 0;
        }
        
        .pagination .page-link {
            color: #333;
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            margin: 0 2px;
        }

        

        .pagination .page-link:hover {
            color: #AD1E23;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .page-item {
            margin-left: 5px;
        }

        .pagination .page-item.active .page-link {
            background-color: #AD1E23;
            border-color: #AD1E23;
            color: #fff;
        }

        .pagination .page-item.active .page-link:hover {
            background-color: #92191d;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<!--#include file="/include/common/home_nav.shtml" -->

    <!-- Banner部分 显示完整的面包屑 -->
    <div class="banner-container position-relative">
        <img src="/images/news_banner.jpg" alt="学院新闻" class="w-100" style="height: 400px; object-fit: cover;">
        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
            <h1 class="display-4 fw-bold" style="font-family: 'Montserrat', sans-serif;">学院新闻</h1>
            <p class="lead">
                <a href="/" class="text-white text-decoration-none">首页</a>
                    <span class="text-white"> / </span>
                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="text-white text-decoration-none">学院新闻</a>
                    <span class="text-white"> / </span>
                            <a href="/jigoushezhi001/" class="text-white text-decoration-none">机构设置</a>
                    <span class="text-white"> / </span>
                            <a href="/erjixueyuan/jigoushezhi001/" class="text-white text-decoration-none">二级学院</a>
                    <span class="text-white"> / </span>
                            <a href="/tiyuyuyishuxueyuan/jigoushezhi001/erjixueyuan/" class="text-white text-decoration-none">体育与艺术学院</a>
            </p>
        </div>
    </div>
   
    <!-- 文章内容 start -->
    <div class="container my-5" style="max-width: 1200px;">
        <div class="row">
            <!-- 左侧导航栏 -->
            <div class="col-md-2">
                <div class="list-group">

                    <!-- 父级栏目名称 -->
                    <div class="list-group-item title-with-line">
                                    <p class="mb-0">体育与艺术学院</p>
                    </div>


                    <!-- 所有子栏目名称 -->
                                <a href="/xueyuangaikuang_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">学院概况</a>
                                <a href="/shiziduiwutyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师资队伍</a>
                                <a href="/guanliduiwu_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">管理队伍</a>
                                <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action active">学院新闻</a>
                                <a href="/tupianxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">图片新闻</a>
                                <a href="/jiaoxuehuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">教学活动</a>
                                <a href="/dangtuanyuxueshenghuodongtyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">党团与学生活动</a>
                                <a href="/shishengfengcaityyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">师生风采</a>
                                <a href="/zhuanyeshezhi_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">专业设置</a>
                                <a href="/zililaoxiazai_tyys/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/" class="list-group-item list-group-item-action ">资料下载</a>
                </div>
            </div>

            <!-- 右侧文章详情 start 放在任何栏目下都可以-->
            <div class="col-md-10">
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card" style="border: none; border-radius: 0;">
                            <div class="row g-0">
                                <!-- 内容部分 -->
                                <div class="col-md-12">
                                    <div class="card-body">
                                        <h2 class="card-title mb-3 text-start">
                                            <a href="/xueyuanxinwentyyysxy/jigoushezhi001/erjixueyuan/tiyuyuyishuxueyuan/310000000006223.shtml" class="text-decoration-none text-dark fw-bold">
                                                学院召开第十九届“挑战杯”全国大学生课外学术科技作品竞赛安排部署会
                                            </a>
                                        </h2>
                                        <p class="card-text text-muted mb-3"></p>

                                        <!-- 小编 日期 浏览数 -->
                                        <div class="d-flex align-items-center text-muted small">
                                            <i class="far fa-user me-2"></i>
                                            <span class="me-3">小编</span>
                                            <i class="far fa-calendar me-2"></i>
                                            <span class="me-3">2024-12-13</span>
                                            <i class="far fa-eye me-2"></i>
                                            <span>196</span>
                                        </div>

                                        <!-- 文章内容 start -->
                                        <div class="mt-5">
                                                <style>img {max-width: 100%;height: auto;}</style><p style="text-indent: 37px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体; font-size: 20px;">为进一步做好学院创新创业相关工作，激发学生的创业热情和竞赛动力。12月12日，学院在师德楼910会议室召开第十九届“挑战杯”全国大学生课外学术科技作品竞赛安排部署会。学院院长王雪出席并主持会议，学院各教研室负责人、党团建设负责人参加此次会议。</span></p><p style="text-align: center; line-height: 2em;"><img src="/uploads/20241213/49ed6bf552d9ec4e8fbcc5168d0e2efa.JPG" alt="会议现场.JPG" style="max-width: 100%; height: auto;"/></p><p style="text-align: center; line-height: 2em;"><span style="font-size: 14px;"><span style="font-family: 宋体;">会议现场</span></span></p><p style="text-align: justify; line-height: 2em;"><span style="font-family: 宋体; font-size: 20px; text-align: center;"><strong>&nbsp;&nbsp;</strong>会上，王雪强调</span><span style="font-family: 宋体; font-size: 20px; text-align: center;">“挑战杯”全国大学生课外学术科技作品竞赛作为极具影响力的全国性竞赛活动，是一个锻炼提升学生的创新能力、综合素质、展示自我、挑战自我的广阔平台，希望学院相关老师高度重视工作的开展和推进。</span></p><p style="line-height: 2em; text-align: center;"><span style="font-family: 宋体; font-size: 20px;"><img src="/uploads/20241213/210564b7fa92efa82dcbf45d5de520d8.JPG" alt="王雪主持会议并进行安排部署.JPG" style="max-width: 100%; height: auto;"/><br/></span></p><p style="text-align: center; line-height: 2em;"><strong><span style="font-size: 14px;"><span style="font-family: 宋体;">王雪主持会议并进行安排部署</span></span></strong></p><p style="line-height: 2em; text-align: justify;"><span style="font-size: 20px;"><strong><span style="font-family: 宋体; font-size: 20px;"></span></strong></span></p><p style="text-indent: 37px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体; font-size: 20px;">会上，王雪还详细解读了第十九届“挑战杯”全国大学生课外学术科技作品竞赛参赛项目类别和要求，并进行了细致全面的部署安排。王雪指出，学院音乐、美术、体育、设计相关专业都是实操性较强，能够在其中挖掘专业特色亮点，找到创新性的参赛点的专业，希望各教研室负责人打开思路，积极动员学生参与，通过将“挑战杯”全国大学生课外学术科技作品竞赛和学生专业实践、假期实践相结合，总结提炼出更多具有创新性的优秀项目进行申报。同时，要将培养学生创业精神、实践能力和专业能力相结合，切实加强对参赛学生的指导和培训，全力提高作品的质量和水平，推动学院创新创业工作蓬勃发展。</span></p><p style="text-indent: 37px; line-height: 2em; text-align: justify;"><span style="font-family: 宋体; font-size: 20px;">一直以来，学院紧跟学校创新创业工作步伐，在各类创新创业赛事上提前部署、积极动员、全面推荐，将各类双创比赛和学生专业实践相结合、相贯通，努力形成有专业特色、有学科亮点的双创项目，通过比赛不断提升学生综和竞争力，努力为学校高质量发展贡献学院力量。</span></p><p style="line-height: 2em; text-align: justify;"><span style="font-size: 20px;"><strong><span style="font-family: 宋体; font-size: 20px;"><br/></span></strong><br/></span></p><p><br/></p>
                                        </div>
                                        <!-- 文章内容 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                </div>
                </div>
                </div>
               
    <!-- 文章内容 end  -->

      <!-- 页脚 -->
<!--#include file="/include/common/home_footer.shtml" -->


  <!-- 添加所有用上的js库 -->
  <!-- Bootstrap 5.3.0 JS Bundle with Popper -->
  <script src="/js/bootstrap.bundle.min.js"></script>
    <!-- 添加图片懒加载 -->
    <script src="/js/common_js_at_footer.js"></script>
</body>
</html>